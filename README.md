# مختبر الصحة العامة المركزي - ذي قار

نظام إدارة شامل للمختبرات الطبية مصمم خصيصاً لمختبر الصحة العامة المركزي في محافظة ذي قار، العراق.

## المميزات الرئيسية

### 🔬 إدخال البيانات
- إدخال بيانات المرضى والعينات مع التحقق من صحة البيانات
- إضافة تحاليل متعددة لكل عينة
- طباعة استيكر تلقائية بعد إدخال البيانات
- استيراد البيانات من ملفات Excel
- البحث والتصفية المتقدمة

### ⚙️ إدارة العمل
- إنشاء وإدارة الوجبات (Batches)
- توزيع العينات على الفنيين
- تتبع حالة العمل والتقدم
- إرسال الوجبات المكتملة للنتائج

### 📊 إدخال النتائج
- إدخال النتائج حسب رقم الوجبة
- نتائج متعددة: Negative, Positive, Retest, Recollection, Sent, TND
- إدخال سريع للنتائج الشائعة
- حفظ الملاحظات لكل نتيجة

### 📈 التقارير والإحصائيات
- تقارير مفردة ومتعددة
- فلترة متقدمة حسب التاريخ، النوع، الجنس، إلخ
- إحصائيات فورية للنتائج
- طباعة التقارير بالعربية مع الشعار الرسمي
- تصدير إلى Excel و PDF

### ⚙️ الإعدادات
- إدارة أنواع العينات
- إدارة جهات الإرسال
- إدارة التحاليل المتاحة
- إدارة بيانات الفنيين
- تخصيص إعدادات التقرير

## متطلبات النظام

### البرمجيات المطلوبة
- Python 3.7 أو أحدث
- نظام التشغيل: Windows 10/11, macOS, أو Linux

### المكتبات المطلوبة
البرنامج يعمل بالمكتبات الأساسية لـ Python فقط:
- tkinter (مدمجة مع Python)
- sqlite3 (مدمجة مع Python)
- csv (مدمجة مع Python)
- datetime (مدمجة مع Python)

## التثبيت والتشغيل

### 1. تحميل البرنامج
قم بتحميل جميع ملفات البرنامج في مجلد واحد:
- `run_lab_fixed.py` (ملف التشغيل الرئيسي)
- `start_lab.bat` (ملف التشغيل السريع)
- `main_window.py`
- `database.py`
- `data_entry_tab.py`
- `work_tab.py`
- `results_tab.py`
- `reports_tab.py`
- `settings_tab.py`

### 2. التأكد من تثبيت Python
تأكد من تثبيت Python 3.7 أو أحدث على النظام.
يمكنك التحقق بتشغيل الأمر:
```bash
py --version
```

### 3. تشغيل البرنامج

#### الطريقة الأولى: استخدام ملف التشغيل السريع
انقر نقراً مزدوجاً على ملف `start_lab.bat`

#### الطريقة الثانية: من موجه الأوامر
```bash
py run_lab_fixed.py
```

## دليل الاستخدام

### البدء السريع
1. شغل البرنامج باستخدام `python run_lab_system.py`
2. ستظهر النافذة الرئيسية مع التبويبات الجانبية
3. ابدأ بإدخال البيانات من تبويب "إدخال البيانات"
4. انتقل إلى تبويب "العمل" لإنشاء الوجبات
5. استخدم تبويب "النتائج" لإدخال نتائج التحاليل
6. اطبع التقارير من تبويب "التقارير والإحصائيات"

### إعداد النظام
1. اذهب إلى تبويب "الإعدادات"
2. أضف أنواع العينات المطلوبة
3. أضف جهات الإرسال
4. أضف التحاليل المتاحة
5. أضف بيانات الفنيين
6. اضبط إعدادات التقرير حسب احتياجاتك

## الميزات التقنية

### قاعدة البيانات
- استخدام SQLite لسهولة النقل والصيانة
- تصميم قاعدة بيانات محسنة للأداء
- نسخ احتياطية تلقائية

### الواجهة
- تصميم عصري وسهل الاستخدام
- دعم كامل للغة العربية
- ألوان مميزة وتصميم ثلاثي الأبعاد للأزرار
- تبويبات جانبية للتنقل السهل

### التقارير
- تقارير PDF عالية الجودة
- دعم الخطوط العربية
- شعار وزارة الصحة العراقية
- تخطيط مخصص للمختبر

## الدعم والصيانة

### حل المشاكل الشائعة
1. **خطأ في المكتبات**: تأكد من تثبيت جميع المكتبات المطلوبة
2. **خطأ في قاعدة البيانات**: تأكد من صلاحيات الكتابة في مجلد البرنامج
3. **مشاكل الطباعة**: تأكد من تثبيت طابعة الاستيكر بشكل صحيح

### النسخ الاحتياطية
- يتم إنشاء ملف `lab_database.db` تلقائياً
- انسخ هذا الملف بانتظام للحفاظ على البيانات
- يمكن نقل الملف إلى أجهزة أخرى

## الأمان والخصوصية

- جميع البيانات محفوظة محلياً على الجهاز
- لا يتم إرسال أي بيانات عبر الإنترنت
- يُنصح بحماية الجهاز بكلمة مرور
- عمل نسخ احتياطية منتظمة

## التطوير المستقبلي

### ميزات مخططة
- [ ] ربط مع أنظمة المستشفيات
- [ ] تقارير إحصائية متقدمة
- [ ] نظام إشعارات
- [ ] دعم عدة مستخدمين
- [ ] تطبيق جوال مصاحب

## الترخيص

هذا البرنامج مطور خصيصاً لمختبر الصحة العامة المركزي في ذي قار ومتاح للاستخدام الحكومي.

## معلومات الاتصال

للدعم التقني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- العنوان: ذي قار / الناصرية / سومر / مجاور حسينية السفراء الأربعة

---

**مختبر الصحة العامة المركزي - ذي قار**  
*وزارة الصحة - جمهورية العراق*
