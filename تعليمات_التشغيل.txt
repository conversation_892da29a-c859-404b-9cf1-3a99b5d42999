========================================
مختبر الصحة العامة المركزي - ذي قار
تعليمات التشغيل السريعة
========================================

✅ البرنامج جاهز للتشغيل!

🚀 طريقة التشغيل:
1. انقر نقراً مزدوجاً على ملف "start_lab.bat"
2. أو افتح موجه الأوامر واكتب: py run_lab_fixed.py

📋 ملفات البرنامج الأساسية:
- run_lab_fixed.py (ملف التشغيل الرئيسي)
- start_lab.bat (تشغيل سريع)
- main_window.py (النافذة الرئيسية)
- database.py (قاعدة البيانات)
- data_entry_tab.py (تبويب إدخال البيانات)
- work_tab.py (تبويب العمل)
- results_tab.py (تبويب النتائج)
- reports_tab.py (تبويب التقارير)
- settings_tab.py (تبويب الإعدادات)
- lab_database.db (ملف قاعدة البيانات)

🔧 المتطلبات:
- Python 3.7 أو أحدث (مثبت مسبقاً)
- لا حاجة لمكتبات إضافية

📖 الدلائل المتوفرة:
- README.md (دليل التثبيت والتشغيل)
- دليل_المستخدم.md (دليل الاستخدام المفصل)
- ملخص_المشروع.md (ملخص شامل للمشروع)

🎯 الميزات الرئيسية:
✓ إدخال بيانات المرضى والعينات
✓ إدارة الوجبات وتوزيع العمل
✓ إدخال النتائج
✓ تقارير وإحصائيات شاملة
✓ إعدادات قابلة للتخصيص
✓ طباعة الاستيكر والتقارير
✓ استيراد/تصدير البيانات

🆘 في حالة المشاكل:
1. تأكد من تثبيت Python: py --version
2. تأكد من وجود جميع الملفات في نفس المجلد
3. تأكد من صلاحيات الكتابة في المجلد
4. راجع ملف دليل_المستخدم.md

📞 للدعم التقني:
<EMAIL>

========================================
وزارة الصحة - جمهورية العراق
مختبر الصحة العامة المركزي - ذي قار
========================================
