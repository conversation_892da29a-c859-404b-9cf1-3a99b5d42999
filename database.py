import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

class DatabaseManager:
    def __init__(self, db_path: str = "lab_database.db"):
        self.db_path = db_path
        self.init_database()
    
    def get_connection(self):
        """إنشاء اتصال بقاعدة البيانات"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المرضى والعينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                national_id INTEGER UNIQUE,
                name TEXT NOT NULL,
                age INTEGER NOT NULL,
                gender TEXT NOT NULL CHECK (gender IN ('M', 'F')),
                address TEXT NOT NULL,
                phone TEXT NOT NULL,
                passport_number TEXT,
                receipt_number TEXT,
                sample_type TEXT NOT NULL,
                sender_organization TEXT NOT NULL,
                sample_collection_date DATE NOT NULL,
                sample_received_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول التحاليل
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول ربط المرضى بالتحاليل
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS patient_tests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                test_id INTEGER,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (test_id) REFERENCES tests (id)
            )
        ''')
        
        # جدول الوجبات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS batches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_number INTEGER UNIQUE,
                work_date DATE DEFAULT CURRENT_DATE,
                technician_name TEXT,
                status TEXT DEFAULT 'في العمل' CHECK (status IN ('في العمل', 'مكتمل', 'مرسل للنتائج')),
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول ربط العينات بالوجبات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS batch_samples (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                batch_id INTEGER,
                patient_id INTEGER,
                test_id INTEGER,
                technician_name TEXT,
                FOREIGN KEY (batch_id) REFERENCES batches (id),
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (test_id) REFERENCES tests (id)
            )
        ''')
        
        # جدول النتائج
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                patient_id INTEGER,
                test_id INTEGER,
                batch_id INTEGER,
                result TEXT CHECK (result IN ('Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND')),
                result_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (patient_id) REFERENCES patients (id),
                FOREIGN KEY (test_id) REFERENCES tests (id),
                FOREIGN KEY (batch_id) REFERENCES batches (id)
            )
        ''')
        
        # جدول أنواع العينات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sample_types (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT
            )
        ''')
        
        # جدول جهات الإرسال
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS sender_organizations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                address TEXT,
                phone TEXT
            )
        ''')
        
        # جدول الفنيين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS technicians (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                specialization TEXT,
                phone TEXT
            )
        ''')
        
        # جدول إعدادات التقرير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS report_settings (
                id INTEGER PRIMARY KEY,
                header_arabic TEXT,
                header_english TEXT,
                footer_address TEXT,
                footer_email TEXT,
                logo_path TEXT
            )
        ''')
        
        # إدراج البيانات الافتراضية
        self._insert_default_data(cursor)
        
        conn.commit()
        conn.close()
    
    def _insert_default_data(self, cursor):
        """إدراج البيانات الافتراضية"""
        # أنواع العينات الافتراضية
        default_sample_types = [
            ('دم', 'عينة دم'),
            ('بول', 'عينة بول'),
            ('براز', 'عينة براز'),
            ('مسحة', 'مسحة طبية'),
            ('بلغم', 'عينة بلغم')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO sample_types (name, description) VALUES (?, ?)
        ''', default_sample_types)
        
        # التحاليل الافتراضية
        default_tests = [
            ('فحص كوفيد-19', 'فحص فيروس كورونا'),
            ('فحص الملاريا', 'فحص طفيلي الملاريا'),
            ('فحص التيفوئيد', 'فحص بكتيريا التيفوئيد'),
            ('فحص الكوليرا', 'فحص بكتيريا الكوليرا'),
            ('فحص الإنفلونزا', 'فحص فيروس الإنفلونزا')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO tests (name, description) VALUES (?, ?)
        ''', default_tests)
        
        # جهات الإرسال الافتراضية
        default_senders = [
            ('مستشفى الناصرية العام', 'الناصرية', '07801234567'),
            ('مستشفى الحبوبي', 'الناصرية', '07801234568'),
            ('مركز صحي الشطرة', 'الشطرة', '07801234569'),
            ('مركز صحي سوق الشيوخ', 'سوق الشيوخ', '07801234570')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO sender_organizations (name, address, phone) VALUES (?, ?, ?)
        ''', default_senders)
        
        # الفنيين الافتراضيين
        default_technicians = [
            ('أحمد محمد', 'فني مختبر أول', '07801234571'),
            ('فاطمة علي', 'فني مختبر ثاني', '07801234572'),
            ('محمد حسن', 'فني مختبر ثالث', '07801234573')
        ]
        
        cursor.executemany('''
            INSERT OR IGNORE INTO technicians (name, specialization, phone) VALUES (?, ?, ?)
        ''', default_technicians)
        
        # إعدادات التقرير الافتراضية
        cursor.execute('''
            INSERT OR IGNORE INTO report_settings (id, header_arabic, header_english, footer_address, footer_email) 
            VALUES (1, ?, ?, ?, ?)
        ''', (
            'جمهورية العراق\nوزارة الصحة\nقسم الصحة العامة\nمختبر الصحة العامة\nوحدة الفايروسات',
            'Republic of Iraq\nMinistry of Health\nPublic Health Department\nPublic Health Laboratory\nVirus Unit',
            'ذي قار /الناصرية /سومر/ مجاور حسينية السفراء الاربعة',
            '<EMAIL>'
        ))
    
    def get_next_national_id(self) -> int:
        """الحصول على الرقم الوطني التالي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT MAX(national_id) FROM patients')
        result = cursor.fetchone()
        conn.close()
        return (result[0] or 0) + 1
    
    def get_next_batch_number(self) -> int:
        """الحصول على رقم الوجبة التالي"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT MAX(batch_number) FROM batches')
        result = cursor.fetchone()
        conn.close()
        return (result[0] or 0) + 1
