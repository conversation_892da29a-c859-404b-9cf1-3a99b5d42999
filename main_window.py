import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont

class MainWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مختبر الصحة العامة المركزي - ذي قار")
        self.root.geometry("1400x900")
        self.root.state('zoomed')  # فتح النافذة بحجم كامل
        
        # إعداد الخطوط
        self.setup_fonts()
        
        # إعداد الألوان والتصميم
        self.setup_theme()
        
        # إنشاء قاعدة البيانات
        try:
            from database import DatabaseManager
            self.db = DatabaseManager()
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات: {str(e)}")
            return

        # إنشاء الواجهة
        self.create_widgets()

        # ربط التبويبات
        self.connect_tabs()
    
    def setup_fonts(self):
        """إعداد الخطوط"""
        self.font = tkFont.Font(family="Arial", size=12, weight="bold")
        self.default_font = tkFont.Font(family="Arial", size=12, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=14, weight="bold")
        self.title_font = tkFont.Font(family="Arial", size=16, weight="bold")
    
    def setup_theme(self):
        """إعداد الألوان والتصميم العصري"""
        # ألوان عصرية
        self.colors = {
            'primary': '#2E86AB',      # أزرق عصري
            'secondary': '#A23B72',    # وردي داكن
            'accent': '#F18F01',       # برتقالي
            'success': '#C73E1D',      # أحمر
            'background': '#F5F5F5',   # رمادي فاتح
            'surface': '#FFFFFF',      # أبيض
            'text': '#2C3E50',         # رمادي داكن
            'text_light': '#7F8C8D',   # رمادي فاتح
            'border': '#BDC3C7'        # رمادي حدود
        }
        
        # إعداد النمط
        style = ttk.Style()
        style.theme_use('clam')
        
        # تخصيص الأزرار
        style.configure('Custom.TButton',
                       font=self.default_font,
                       padding=(10, 5),
                       relief='raised',
                       borderwidth=2)
        
        # تخصيص التبويبات
        style.configure('Custom.TNotebook.Tab',
                       font=self.default_font,
                       padding=(15, 8))
        
        self.root.configure(bg=self.colors['background'])
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان البرنامج
        title_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=80)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="مختبر الصحة العامة المركزي - ذي قار",
                              font=self.title_font,
                              bg=self.colors['primary'],
                              fg='white')
        title_label.pack(expand=True)
        
        # إطار المحتوى الرئيسي
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار التبويبات الجانبية
        self.sidebar_frame = tk.Frame(content_frame, 
                                     bg=self.colors['surface'], 
                                     width=200,
                                     relief=tk.RAISED,
                                     borderwidth=2)
        self.sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        self.sidebar_frame.pack_propagate(False)
        
        # إطار المحتوى
        self.content_frame = tk.Frame(content_frame, 
                                     bg=self.colors['surface'],
                                     relief=tk.RAISED,
                                     borderwidth=2)
        self.content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # إنشاء التبويبات الجانبية
        self.create_sidebar_tabs()
        
        # إنشاء محتوى التبويبات
        self.create_tab_contents()
        
        # عرض التبويب الأول افتراضياً
        self.show_tab('data_entry')
    
    def create_sidebar_tabs(self):
        """إنشاء التبويبات الجانبية"""
        # عنوان التبويبات
        sidebar_title = tk.Label(self.sidebar_frame,
                                text="القوائم",
                                font=self.header_font,
                                bg=self.colors['surface'],
                                fg=self.colors['text'])
        sidebar_title.pack(pady=10)
        
        # قائمة التبويبات
        self.tabs = [
            ('data_entry', 'إدخال البيانات', self.colors['primary']),
            ('work', 'العمل', self.colors['secondary']),
            ('results', 'النتائج', self.colors['accent']),
            ('reports', 'التقارير والإحصائيات', self.colors['success']),
            ('settings', 'الإعدادات', self.colors['text'])
        ]
        
        self.tab_buttons = {}
        
        for tab_id, tab_name, color in self.tabs:
            btn = tk.Button(self.sidebar_frame,
                           text=tab_name,
                           font=self.font,
                           bg=color,
                           fg='white',
                           activebackground=color,
                           activeforeground='white',
                           relief=tk.RAISED,
                           borderwidth=3,
                           width=18,
                           height=2,
                           command=lambda t=tab_id: self.show_tab(t))
            btn.pack(pady=5, padx=10, fill=tk.X)
            self.tab_buttons[tab_id] = btn
    
    def create_tab_contents(self):
        """إنشاء محتوى التبويبات"""
        self.tab_frames = {}

        try:
            # تبويب إدخال البيانات
            from data_entry_tab import DataEntryTab
            self.tab_frames['data_entry'] = DataEntryTab(self.content_frame, self.db, self)

            # تبويب العمل
            from work_tab import WorkTab
            self.tab_frames['work'] = WorkTab(self.content_frame, self.db, self)

            # تبويب النتائج
            from results_tab import ResultsTab
            self.tab_frames['results'] = ResultsTab(self.content_frame, self.db, self)

            # تبويب التقارير
            from reports_tab import ReportsTab
            self.tab_frames['reports'] = ReportsTab(self.content_frame, self.db, self)

            # تبويب الإعدادات
            from settings_tab import SettingsTab
            self.tab_frames['settings'] = SettingsTab(self.content_frame, self.db, self)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل التبويبات: {str(e)}")
            # إنشاء تبويب بسيط في حالة الخطأ
            self.create_simple_tab()
    
    def show_tab(self, tab_id):
        """عرض التبويب المحدد"""
        try:
            # إخفاء جميع التبويبات
            for frame in self.tab_frames.values():
                if hasattr(frame, 'hide'):
                    frame.hide()
                else:
                    frame.pack_forget()

            # إعادة تعيين ألوان الأزرار
            for btn_id, btn in self.tab_buttons.items():
                if btn_id == tab_id:
                    btn.configure(relief=tk.SUNKEN, borderwidth=1)
                else:
                    btn.configure(relief=tk.RAISED, borderwidth=3)

            # عرض التبويب المحدد
            if tab_id in self.tab_frames:
                if hasattr(self.tab_frames[tab_id], 'show'):
                    self.tab_frames[tab_id].show()
                else:
                    self.tab_frames[tab_id].pack(fill=tk.BOTH, expand=True)
                self.current_tab = tab_id
            else:
                # عرض رسالة في حالة عدم وجود التبويب
                messagebox.showinfo("معلومات", f"التبويب {tab_id} غير متاح حالياً")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التبويب: {str(e)}")
    
    def connect_tabs(self):
        """ربط التبويبات مع بعضها البعض"""
        # سيتم تنفيذ هذا لاحقاً لضمان تحديث البيانات بين التبويبات
        pass
    
    def create_simple_tab(self):
        """إنشاء تبويب بسيط في حالة الخطأ"""
        simple_frame = tk.Frame(self.content_frame, bg=self.colors['surface'])

        label = tk.Label(simple_frame,
                        text="مرحباً بك في مختبر الصحة العامة المركزي - ذي قار\n\nحدث خطأ في تحميل التبويبات الكاملة\nيرجى التحقق من الملفات",
                        font=self.header_font,
                        bg=self.colors['surface'],
                        fg=self.colors['text'],
                        justify=tk.CENTER)
        label.pack(expand=True)

        self.tab_frames['simple'] = simple_frame

    def refresh_all_tabs(self):
        """تحديث جميع التبويبات"""
        for tab in self.tab_frames.values():
            if hasattr(tab, 'refresh'):
                tab.refresh()

    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

if __name__ == "__main__":
    app = MainWindow()
    app.run()
