import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from datetime import datetime, date
from typing import List, Dict
import os
import csv

class ReportsTab:
    def __init__(self, parent, db, main_window):
        self.parent = parent
        self.db = db
        self.main_window = main_window
        self.frame = None

        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=10, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=12, weight="bold")

        # الألوان
        self.colors = main_window.colors

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر واجهة التقارير"""
        self.frame = tk.Frame(self.parent, bg=self.colors['surface'])

        # عنوان التبويب
        title_label = tk.Label(self.frame,
                              text="التقارير والإحصائيات",
                              font=self.header_font,
                              bg=self.colors['surface'],
                              fg=self.colors['text'])
        title_label.pack(pady=10)

        # إطار رئيسي للمحتوى
        main_content = tk.Frame(self.frame, bg=self.colors['surface'])
        main_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إطار الفلاتر
        filters_frame = tk.LabelFrame(main_content,
                                     text="فلاتر البحث",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'])
        filters_frame.pack(fill=tk.X, pady=10)

        self.create_filters_section(filters_frame)

        # إطار النتائج
        results_frame = tk.LabelFrame(main_content,
                                     text="النتائج",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'])
        results_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.create_results_section(results_frame)

        # إطار الأزرار
        buttons_frame = tk.Frame(main_content, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, pady=10)

        self.create_buttons_section(buttons_frame)

    def create_filters_section(self, parent):
        """إنشاء قسم الفلاتر"""
        # الصف الأول - التواريخ
        dates_frame = tk.Frame(parent, bg=self.colors['surface'])
        dates_frame.pack(fill=tk.X, padx=10, pady=10)

        # من تاريخ
        tk.Label(dates_frame, text="من تاريخ:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.from_date_var = tk.StringVar()
        self.from_date_var.set((date.today().replace(day=1)).strftime('%Y-%m-%d'))
        from_date_entry = tk.Entry(dates_frame, textvariable=self.from_date_var,
                                  font=self.font, width=12)
        from_date_entry.pack(side=tk.LEFT, padx=5)

        # إلى تاريخ
        tk.Label(dates_frame, text="إلى تاريخ:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.to_date_var = tk.StringVar()
        self.to_date_var.set(date.today().strftime('%Y-%m-%d'))
        to_date_entry = tk.Entry(dates_frame, textvariable=self.to_date_var,
                                font=self.font, width=12)
        to_date_entry.pack(side=tk.LEFT, padx=5)

        # الصف الثاني - الفلاتر الأخرى
        filters_row2 = tk.Frame(parent, bg=self.colors['surface'])
        filters_row2.pack(fill=tk.X, padx=10, pady=5)

        # الاسم
        tk.Label(filters_row2, text="الاسم:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.name_filter_var = tk.StringVar()
        name_entry = tk.Entry(filters_row2, textvariable=self.name_filter_var,
                             font=self.font, width=20)
        name_entry.pack(side=tk.LEFT, padx=5)

        # نوع العينة
        tk.Label(filters_row2, text="نوع العينة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.sample_type_filter = ttk.Combobox(filters_row2, font=self.font, width=15, state='readonly')
        self.sample_type_filter.pack(side=tk.LEFT, padx=5)

        # الصف الثالث - المزيد من الفلاتر
        filters_row3 = tk.Frame(parent, bg=self.colors['surface'])
        filters_row3.pack(fill=tk.X, padx=10, pady=5)

        # الجنس
        tk.Label(filters_row3, text="الجنس:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.gender_filter = ttk.Combobox(filters_row3, font=self.font, width=10, state='readonly')
        self.gender_filter['values'] = ('الكل', 'ذكر', 'أنثى')
        self.gender_filter.set('الكل')
        self.gender_filter.pack(side=tk.LEFT, padx=5)

        # جهة الإرسال
        tk.Label(filters_row3, text="جهة الإرسال:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.sender_filter = ttk.Combobox(filters_row3, font=self.font, width=20, state='readonly')
        self.sender_filter.pack(side=tk.LEFT, padx=5)

        # النتيجة
        tk.Label(filters_row3, text="النتيجة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.result_filter = ttk.Combobox(filters_row3, font=self.font, width=12, state='readonly')
        self.result_filter['values'] = ('الكل', 'Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND')
        self.result_filter.set('الكل')
        self.result_filter.pack(side=tk.LEFT, padx=5)

        # زر البحث
        search_btn = tk.Button(filters_row3, text="بحث", font=self.font,
                              bg=self.colors['primary'], fg='white', width=10, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.search_reports)
        search_btn.pack(side=tk.LEFT, padx=20)

        # تحميل قيم الفلاتر
        self.load_filter_values()

    def create_results_section(self, parent):
        """إنشاء قسم النتائج"""
        # إطار الإحصائيات
        stats_frame = tk.Frame(parent, bg=self.colors['surface'])
        stats_frame.pack(fill=tk.X, padx=10, pady=10)

        # عنوان الإحصائيات
        tk.Label(stats_frame, text="الإحصائيات:", font=self.header_font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(anchor='w')

        # إطار أرقام الإحصائيات
        stats_numbers_frame = tk.Frame(stats_frame, bg=self.colors['surface'])
        stats_numbers_frame.pack(fill=tk.X, pady=5)

        # إجمالي العينات
        self.total_samples_var = tk.StringVar()
        self.total_samples_var.set("إجمالي العينات: 0")
        tk.Label(stats_numbers_frame, textvariable=self.total_samples_var, font=self.font,
                bg=self.colors['primary'], fg='white', width=20, height=2,
                relief=tk.RAISED, borderwidth=2).pack(side=tk.LEFT, padx=5)

        # النتائج الإيجابية
        self.positive_results_var = tk.StringVar()
        self.positive_results_var.set("النتائج الإيجابية: 0")
        tk.Label(stats_numbers_frame, textvariable=self.positive_results_var, font=self.font,
                bg=self.colors['success'], fg='white', width=20, height=2,
                relief=tk.RAISED, borderwidth=2).pack(side=tk.LEFT, padx=5)

        # النتائج السلبية
        self.negative_results_var = tk.StringVar()
        self.negative_results_var.set("النتائج السلبية: 0")
        tk.Label(stats_numbers_frame, textvariable=self.negative_results_var, font=self.font,
                bg=self.colors['secondary'], fg='white', width=20, height=2,
                relief=tk.RAISED, borderwidth=2).pack(side=tk.LEFT, padx=5)

        # النتائج المعلقة
        self.pending_results_var = tk.StringVar()
        self.pending_results_var.set("النتائج المعلقة: 0")
        tk.Label(stats_numbers_frame, textvariable=self.pending_results_var, font=self.font,
                bg=self.colors['accent'], fg='white', width=20, height=2,
                relief=tk.RAISED, borderwidth=2).pack(side=tk.LEFT, padx=5)

        # جدول النتائج
        table_frame = tk.Frame(parent, bg=self.colors['surface'])
        table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # أعمدة الجدول
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'التحليل',
                  'جهة الإرسال', 'تاريخ السحب', 'تاريخ الاستلام', 'النتيجة', 'تاريخ النتيجة')

        self.results_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # إعداد العناوين
        for col in columns:
            self.results_tree.heading(col, text=col)
            if col in ['العمر', 'الجنس']:
                self.results_tree.column(col, width=60, anchor='center')
            elif col in ['النتيجة']:
                self.results_tree.column(col, width=80, anchor='center')
            else:
                self.results_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        results_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scrollbar.set)

        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_buttons_section(self, parent):
        """إنشاء قسم الأزرار"""
        # الصف الأول من الأزرار
        buttons_row1 = tk.Frame(parent, bg=self.colors['surface'])
        buttons_row1.pack(pady=5)

        print_individual_btn = tk.Button(buttons_row1, text="طباعة تقرير مفرد", font=self.font,
                                        bg=self.colors['primary'], fg='white', width=15, height=2,
                                        relief=tk.RAISED, borderwidth=3,
                                        command=self.print_individual_report)
        print_individual_btn.pack(side=tk.LEFT, padx=5)

        print_multiple_btn = tk.Button(buttons_row1, text="طباعة تقرير متعدد", font=self.font,
                                      bg=self.colors['secondary'], fg='white', width=15, height=2,
                                      relief=tk.RAISED, borderwidth=3,
                                      command=self.print_multiple_report)
        print_multiple_btn.pack(side=tk.LEFT, padx=5)

        preview_btn = tk.Button(buttons_row1, text="معاينة التقرير", font=self.font,
                               bg=self.colors['accent'], fg='white', width=12, height=2,
                               relief=tk.RAISED, borderwidth=3,
                               command=self.preview_report)
        preview_btn.pack(side=tk.LEFT, padx=5)

        # الصف الثاني من الأزرار
        buttons_row2 = tk.Frame(parent, bg=self.colors['surface'])
        buttons_row2.pack(pady=5)

        export_excel_btn = tk.Button(buttons_row2, text="تصدير إلى Excel", font=self.font,
                                    bg=self.colors['success'], fg='white', width=15, height=2,
                                    relief=tk.RAISED, borderwidth=3,
                                    command=self.export_to_excel)
        export_excel_btn.pack(side=tk.LEFT, padx=5)

        export_pdf_btn = tk.Button(buttons_row2, text="تصدير إلى PDF", font=self.font,
                                  bg=self.colors['text'], fg='white', width=15, height=2,
                                  relief=tk.RAISED, borderwidth=3,
                                  command=self.export_to_pdf)
        export_pdf_btn.pack(side=tk.LEFT, padx=5)

        clear_btn = tk.Button(buttons_row2, text="مسح الفلاتر", font=self.font,
                             bg='#6C757D', fg='white', width=12, height=2,
                             relief=tk.RAISED, borderwidth=3,
                             command=self.clear_filters)
        clear_btn.pack(side=tk.LEFT, padx=5)

    def load_filter_values(self):
        """تحميل قيم الفلاتر"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # تحميل أنواع العينات
        cursor.execute('SELECT DISTINCT name FROM sample_types ORDER BY name')
        sample_types = ['الكل'] + [row[0] for row in cursor.fetchall()]
        self.sample_type_filter['values'] = sample_types
        self.sample_type_filter.set('الكل')

        # تحميل جهات الإرسال
        cursor.execute('SELECT DISTINCT name FROM sender_organizations ORDER BY name')
        senders = ['الكل'] + [row[0] for row in cursor.fetchall()]
        self.sender_filter['values'] = senders
        self.sender_filter.set('الكل')

        conn.close()

    def search_reports(self):
        """البحث في التقارير"""
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        name_filter = self.name_filter_var.get().strip()
        sample_type_filter = self.sample_type_filter.get()
        gender_filter = self.gender_filter.get()
        sender_filter = self.sender_filter.get()
        result_filter = self.result_filter.get()

        conn = self.db.get_connection()
        cursor = conn.cursor()

        # بناء الاستعلام
        query = '''
            SELECT p.national_id, p.name, p.age, p.gender, p.sample_type, t.name as test_name,
                   p.sender_organization, p.sample_collection_date, p.sample_received_date,
                   r.result, r.result_date
            FROM patients p
            JOIN patient_tests pt ON p.id = pt.patient_id
            JOIN tests t ON pt.test_id = t.id
            LEFT JOIN results r ON r.patient_id = p.id AND r.test_id = t.id
            WHERE p.sample_collection_date BETWEEN ? AND ?
        '''
        params = [from_date, to_date]

        # إضافة فلاتر إضافية
        if name_filter:
            query += ' AND p.name LIKE ?'
            params.append(f'%{name_filter}%')

        if sample_type_filter != 'الكل':
            query += ' AND p.sample_type = ?'
            params.append(sample_type_filter)

        if gender_filter != 'الكل':
            gender_code = 'M' if gender_filter == 'ذكر' else 'F'
            query += ' AND p.gender = ?'
            params.append(gender_code)

        if sender_filter != 'الكل':
            query += ' AND p.sender_organization = ?'
            params.append(sender_filter)

        if result_filter != 'الكل':
            query += ' AND r.result = ?'
            params.append(result_filter)

        query += ' ORDER BY p.sample_collection_date DESC, p.national_id'

        cursor.execute(query, params)
        results = cursor.fetchall()

        # حساب الإحصائيات
        self.calculate_statistics(cursor, params[:-1] if result_filter != 'الكل' else params)

        conn.close()

        # مسح النتائج السابقة
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            national_id, name, age, gender, sample_type, test_name, sender_org, collection_date, received_date, result, result_date = row

            gender_text = 'ذكر' if gender == 'M' else 'أنثى'
            result_display = result or 'لم يتم إدخال النتيجة'
            result_date_display = result_date or ''

            self.results_tree.insert('', tk.END, values=(
                national_id, name, age, gender_text, sample_type, test_name,
                sender_org, collection_date, received_date, result_display, result_date_display
            ))

    def calculate_statistics(self, cursor, base_params):
        """حساب الإحصائيات"""
        base_query = '''
            FROM patients p
            JOIN patient_tests pt ON p.id = pt.patient_id
            JOIN tests t ON pt.test_id = t.id
            LEFT JOIN results r ON r.patient_id = p.id AND r.test_id = t.id
            WHERE p.sample_collection_date BETWEEN ? AND ?
        '''

        # إجمالي العينات
        cursor.execute(f'SELECT COUNT(*) {base_query}', base_params)
        total_samples = cursor.fetchone()[0]
        self.total_samples_var.set(f"إجمالي العينات: {total_samples}")

        # النتائج الإيجابية
        cursor.execute(f'SELECT COUNT(*) {base_query} AND r.result = "Positive"', base_params)
        positive_results = cursor.fetchone()[0]
        self.positive_results_var.set(f"النتائج الإيجابية: {positive_results}")

        # النتائج السلبية
        cursor.execute(f'SELECT COUNT(*) {base_query} AND r.result = "Negative"', base_params)
        negative_results = cursor.fetchone()[0]
        self.negative_results_var.set(f"النتائج السلبية: {negative_results}")

        # النتائج المعلقة
        cursor.execute(f'SELECT COUNT(*) {base_query} AND r.result IS NULL', base_params)
        pending_results = cursor.fetchone()[0]
        self.pending_results_var.set(f"النتائج المعلقة: {pending_results}")

    def print_individual_report(self):
        """طباعة تقرير مفرد"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض لطباعة التقرير")
            return

        item = self.results_tree.item(selection[0])
        patient_data = item['values']

        self.generate_individual_report(patient_data)

    def print_multiple_report(self):
        """طباعة تقرير متعدد"""
        # جمع جميع البيانات المعروضة
        all_data = []
        for item in self.results_tree.get_children():
            values = self.results_tree.item(item, 'values')
            all_data.append(values)

        if not all_data:
            messagebox.showwarning("تحذير", "لا توجد بيانات لطباعتها")
            return

        self.generate_multiple_report(all_data)

    def preview_report(self):
        """معاينة التقرير"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض لمعاينة التقرير")
            return

        item = self.results_tree.item(selection[0])
        patient_data = item['values']

        # إنشاء نافذة معاينة
        preview_window = tk.Toplevel(self.frame)
        preview_window.title("معاينة التقرير")
        preview_window.geometry("600x800")
        preview_window.configure(bg='white')

        # محتوى التقرير
        self.create_report_preview(preview_window, patient_data)

    def create_report_preview(self, window, patient_data):
        """إنشاء معاينة التقرير"""
        # إطار التقرير
        report_frame = tk.Frame(window, bg='white', relief=tk.RAISED, borderwidth=2)
        report_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الهيدر العربي
        arabic_header = tk.Label(report_frame,
                                text="جمهورية العراق\nوزارة الصحة\nقسم الصحة العامة\nمختبر الصحة العامة\nوحدة الفايروسات",
                                font=tkFont.Font(family="Arial", size=12, weight="bold"),
                                bg='white', fg='black', justify='center')
        arabic_header.pack(pady=10)

        # الهيدر الإنجليزي
        english_header = tk.Label(report_frame,
                                 text="Republic of Iraq\nMinistry of Health\nPublic Health Department\nPublic Health Laboratory\nVirus Unit",
                                 font=tkFont.Font(family="Arial", size=10),
                                 bg='white', fg='black', justify='center')
        english_header.pack(pady=10)

        # خط فاصل
        separator = tk.Frame(report_frame, height=2, bg='black')
        separator.pack(fill=tk.X, pady=10)

        # بيانات المريض
        patient_info_frame = tk.Frame(report_frame, bg='white')
        patient_info_frame.pack(fill=tk.X, pady=20)

        # استخراج البيانات
        national_id, name, age, gender, sample_type, test_name, sender_org, collection_date, received_date, result, result_date = patient_data

        # عرض البيانات
        info_text = f"""
اسم المريض: {name}
العمر: {age} سنة
الجنس: {gender}
نوع العينة: {sample_type}
التحليل: {test_name}
جهة الإرسال: {sender_org}
تاريخ سحب العينة: {collection_date}
تاريخ استلام العينة: {received_date}
النتيجة: {result}
تاريخ النتيجة: {result_date if result_date else 'لم يتم تحديد'}
تاريخ طباعة التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        info_label = tk.Label(patient_info_frame, text=info_text,
                             font=tkFont.Font(family="Arial", size=11),
                             bg='white', fg='black', justify='right')
        info_label.pack(anchor='e', padx=20)

        # الفوتر
        footer_frame = tk.Frame(report_frame, bg='white')
        footer_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=10)

        # العنوان والإيميل
        footer_left = tk.Label(footer_frame,
                              text="ذي قار /الناصرية /سومر/ مجاور حسينية السفراء الاربعة",
                              font=tkFont.Font(family="Arial", size=9),
                              bg='white', fg='black')
        footer_left.pack(side=tk.LEFT)

        footer_right = tk.Label(footer_frame,
                               text="<EMAIL>",
                               font=tkFont.Font(family="Arial", size=9),
                               bg='white', fg='black')
        footer_right.pack(side=tk.RIGHT)

    def generate_individual_report(self, patient_data):
        """إنشاء تقرير مفرد بصيغة نص"""
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ التقرير",
                defaultextension=".txt",
                filetypes=[("Text files", "*.txt")]
            )

            if not file_path:
                return

            # بيانات المريض
            national_id, name, age, gender, sample_type, test_name, sender_org, collection_date, received_date, result, result_date = patient_data

            # إنشاء محتوى التقرير
            report_content = f"""
========================================
    مختبر الصحة العامة المركزي - ذي قار
========================================

جمهورية العراق
وزارة الصحة
قسم الصحة العامة
مختبر الصحة العامة
وحدة الفايروسات

Republic of Iraq
Ministry of Health
Public Health Department
Public Health Laboratory
Virus Unit

========================================
            تقرير نتيجة التحليل
========================================

الرقم الوطني: {national_id}
اسم المريض: {name}
العمر: {age} سنة
الجنس: {gender}
نوع العينة: {sample_type}
التحليل: {test_name}
جهة الإرسال: {sender_org}
تاريخ سحب العينة: {collection_date}
تاريخ استلام العينة: {received_date}
النتيجة: {result}
تاريخ النتيجة: {result_date if result_date else 'لم يتم تحديد'}
تاريخ طباعة التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

========================================

العنوان: ذي قار /الناصرية /سومر/ مجاور حسينية السفراء الاربعة
البريد الإلكتروني: <EMAIL>

========================================
            """

            # كتابة التقرير
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            messagebox.showinfo("نجح", f"تم إنشاء التقرير بنجاح: {file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def generate_multiple_report(self, all_data):
        """إنشاء تقرير متعدد بصيغة CSV"""
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ التقرير المتعدد",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv")]
            )

            if not file_path:
                return

            # إنشاء التقرير
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة الهيدر
                writer.writerow(['مختبر الصحة العامة المركزي - ذي قار'])
                writer.writerow(['تاريخ التقرير: ' + datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                writer.writerow(['عدد العينات: ' + str(len(all_data))])
                writer.writerow([])  # سطر فارغ

                # كتابة عناوين الأعمدة
                headers = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'التحليل', 'جهة الإرسال', 'تاريخ السحب', 'تاريخ الاستلام', 'النتيجة', 'تاريخ النتيجة']
                writer.writerow(headers)

                # كتابة البيانات
                for row in all_data:
                    writer.writerow(row)

            messagebox.showinfo("نجح", f"تم إنشاء التقرير المتعدد بنجاح: {file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء التقرير: {str(e)}")

    def export_to_excel(self):
        """تصدير البيانات إلى CSV"""
        # جمع البيانات المعروضة
        data = []
        for item in self.results_tree.get_children():
            values = self.results_tree.item(item, 'values')
            data.append(values)

        if not data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف CSV",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv")]
            )

            if not file_path:
                return

            # تصدير إلى CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة العناوين
                headers = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'التحليل',
                          'جهة الإرسال', 'تاريخ السحب', 'تاريخ الاستلام', 'النتيجة', 'تاريخ النتيجة']
                writer.writerow(headers)

                # كتابة البيانات
                for row in data:
                    writer.writerow(row)

            messagebox.showinfo("نجح", f"تم تصدير البيانات إلى CSV: {file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير: {str(e)}")

    def export_to_pdf(self):
        """تصدير البيانات إلى ملف نصي"""
        # جمع البيانات المعروضة
        data = []
        for item in self.results_tree.get_children():
            values = self.results_tree.item(item, 'values')
            data.append(values)

        if not data:
            messagebox.showwarning("تحذير", "لا توجد بيانات للتصدير")
            return

        self.generate_multiple_report(data)

    def clear_filters(self):
        """مسح جميع الفلاتر"""
        self.from_date_var.set((date.today().replace(day=1)).strftime('%Y-%m-%d'))
        self.to_date_var.set(date.today().strftime('%Y-%m-%d'))
        self.name_filter_var.set('')
        self.sample_type_filter.set('الكل')
        self.gender_filter.set('الكل')
        self.sender_filter.set('الكل')
        self.result_filter.set('الكل')

        # مسح النتائج
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # إعادة تعيين الإحصائيات
        self.total_samples_var.set("إجمالي العينات: 0")
        self.positive_results_var.set("النتائج الإيجابية: 0")
        self.negative_results_var.set("النتائج السلبية: 0")
        self.pending_results_var.set("النتائج المعلقة: 0")

    def show(self):
        """عرض التبويب"""
        self.frame.pack(fill=tk.BOTH, expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.frame.pack_forget()

    def refresh(self):
        """تحديث التبويب"""
        self.load_filter_values()