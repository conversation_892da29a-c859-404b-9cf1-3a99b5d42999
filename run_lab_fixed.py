#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مختبر الصحة العامة المركزي - ذي قار
نظام إدارة شامل للمختبرات الطبية

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        print("بدء تشغيل برنامج مختبر الصحة العامة المركزي - ذي قار...")
        
        # اختبار استيراد المكتبات الأساسية
        print("اختبار المكتبات الأساسية...")
        import tkinter as tk
        from tkinter import ttk, messagebox
        print("✓ تم تحميل مكتبات tkinter بنجاح")
        
        # اختبار استيراد قاعدة البيانات
        print("اختبار قاعدة البيانات...")
        from database import DatabaseManager
        db = DatabaseManager()
        print("✓ تم إنشاء قاعدة البيانات بنجاح")
        
        # اختبار استيراد النافذة الرئيسية
        print("تحميل النافذة الرئيسية...")
        from main_window import MainWindow
        print("✓ تم تحميل النافذة الرئيسية بنجاح")
        
        # إنشاء وتشغيل التطبيق
        print("إنشاء التطبيق...")
        app = MainWindow()
        print("✓ تم إنشاء التطبيق بنجاح")
        
        # إعداد معالج الأخطاء
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            print(f"خطأ في البرنامج: {error_msg}")
            messagebox.showerror("خطأ في البرنامج", f"حدث خطأ غير متوقع:\n\n{str(exc_value)}")
        
        sys.excepthook = handle_exception
        
        print("تشغيل البرنامج...")
        print("=" * 50)
        print("البرنامج يعمل الآن!")
        print("=" * 50)
        
        # تشغيل البرنامج
        app.run()
        
    except ImportError as e:
        error_msg = f"""
خطأ في استيراد الملفات:
{str(e)}

يرجى التأكد من وجود جميع ملفات البرنامج في نفس المجلد:
- main_window.py
- database.py
- data_entry_tab.py
- work_tab.py
- results_tab.py
- reports_tab.py
- settings_tab.py
        """
        
        print(error_msg)
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في الملفات", error_msg)
            root.destroy()
        except:
            pass
        
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"""
حدث خطأ أثناء تشغيل البرنامج:
{str(e)}

يرجى التأكد من:
1. وجود جميع ملفات البرنامج
2. صلاحيات الكتابة في مجلد البرنامج
3. عدم وجود ملفات تالفة
        """
        
        print(error_msg)
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
            root.destroy()
        except:
            pass
        
        sys.exit(1)

if __name__ == "__main__":
    main()
