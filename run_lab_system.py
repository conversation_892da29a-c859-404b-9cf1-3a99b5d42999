#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
برنامج مختبر الصحة العامة المركزي - ذي قار
نظام إدارة شامل للمختبرات الطبية

المطور: مساعد الذكي الاصطناعي
التاريخ: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import traceback

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """التحقق من وجود المكتبات المطلوبة"""
    # البرنامج يعمل بالمكتبات الأساسية فقط
    return True

def main():
    """الدالة الرئيسية لتشغيل البرنامج"""
    try:
        # التحقق من المكتبات المطلوبة
        if not check_dependencies():
            sys.exit(1)
        
        # استيراد النافذة الرئيسية
        from main_window import MainWindow
        
        # إنشاء وتشغيل التطبيق
        app = MainWindow()
        
        # إعداد معالج الأخطاء
        def handle_exception(exc_type, exc_value, exc_traceback):
            if issubclass(exc_type, KeyboardInterrupt):
                sys.__excepthook__(exc_type, exc_value, exc_traceback)
                return
            
            error_msg = "".join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            messagebox.showerror("خطأ في البرنامج", f"حدث خطأ غير متوقع:\n\n{error_msg}")
        
        sys.excepthook = handle_exception
        
        # تشغيل البرنامج
        app.run()
        
    except ImportError as e:
        error_msg = f"""
خطأ في استيراد الملفات:
{str(e)}

يرجى التأكد من وجود جميع ملفات البرنامج في نفس المجلد:
- main_window.py
- database.py
- data_entry_tab.py
- work_tab.py
- results_tab.py
- reports_tab.py
- settings_tab.py
        """
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في الملفات", error_msg)
        root.destroy()
        sys.exit(1)
        
    except Exception as e:
        error_msg = f"""
حدث خطأ أثناء تشغيل البرنامج:
{str(e)}

يرجى التأكد من:
1. وجود جميع ملفات البرنامج
2. تثبيت جميع المكتبات المطلوبة
3. صلاحيات الكتابة في مجلد البرنامج
        """
        
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)
        root.destroy()
        sys.exit(1)

if __name__ == "__main__":
    main()
