#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont

class TestButtonsApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار الأزرار - مختبر الصحة العامة")
        self.root.geometry("1000x700")
        
        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=12, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=14, weight="bold")
        
        # الألوان
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72', 
            'accent': '#F18F01',
            'success': '#C73E1D',
            'background': '#F5F5F5',
            'surface': '#FFFFFF',
            'text': '#2C3E50'
        }
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # عنوان
        title_label = tk.Label(main_frame,
                              text="اختبار أزرار إدخال البيانات",
                              font=self.header_font,
                              bg=self.colors['background'],
                              fg=self.colors['text'])
        title_label.pack(pady=20)
        
        # إطار الأزرار الرئيسية
        main_buttons_frame = tk.Frame(main_frame, bg=self.colors['surface'], 
                                     relief=tk.RAISED, borderwidth=2)
        main_buttons_frame.pack(fill=tk.X, pady=10, padx=10)
        
        # عنوان الأزرار الرئيسية
        tk.Label(main_buttons_frame, text="الأزرار الرئيسية:", 
                font=self.font, bg=self.colors['surface'], 
                fg=self.colors['text']).pack(pady=10)
        
        # الصف الأول من الأزرار
        buttons_row1 = tk.Frame(main_buttons_frame, bg=self.colors['surface'])
        buttons_row1.pack(pady=10)
        
        main_buttons = [
            ('💾 حفظ العينة', self.colors['primary'], self.save_sample),
            ('✏️ تعديل', self.colors['secondary'], self.edit_sample),
            ('🗑️ حذف', self.colors['success'], self.delete_sample),
            ('🆕 جديد', self.colors['accent'], self.new_sample),
            ('🏷️ طباعة استيكر', self.colors['text'], self.print_sticker)
        ]
        
        for text, color, command in main_buttons:
            btn = tk.Button(buttons_row1, text=text, font=self.font,
                           bg=color, fg='white', width=15, height=3,
                           relief=tk.RAISED, borderwidth=3,
                           command=command)
            btn.pack(side=tk.LEFT, padx=10)
        
        # إطار أزرار الاستيراد والتصدير
        import_frame = tk.Frame(main_frame, bg=self.colors['surface'], 
                               relief=tk.RAISED, borderwidth=2)
        import_frame.pack(fill=tk.X, pady=10, padx=10)
        
        # عنوان أزرار الاستيراد
        tk.Label(import_frame, text="أزرار الاستيراد والتصدير:", 
                font=self.font, bg=self.colors['surface'], 
                fg=self.colors['text']).pack(pady=10)
        
        # الصف الثاني من الأزرار
        buttons_row2 = tk.Frame(import_frame, bg=self.colors['surface'])
        buttons_row2.pack(pady=10)
        
        import_export_buttons = [
            ('📥 استيراد من CSV', self.colors['primary'], self.import_csv),
            ('📤 تصدير إلى CSV', self.colors['secondary'], self.export_csv),
            ('🔄 تحديث القوائم', self.colors['accent'], self.refresh_data)
        ]
        
        for text, color, command in import_export_buttons:
            btn = tk.Button(buttons_row2, text=text, font=self.font,
                           bg=color, fg='white', width=18, height=3,
                           relief=tk.RAISED, borderwidth=3,
                           command=command)
            btn.pack(side=tk.LEFT, padx=10)
        
        # منطقة النتائج
        result_frame = tk.Frame(main_frame, bg=self.colors['surface'], 
                               relief=tk.RAISED, borderwidth=2)
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10, padx=10)
        
        tk.Label(result_frame, text="منطقة النتائج:", 
                font=self.font, bg=self.colors['surface'], 
                fg=self.colors['text']).pack(pady=10)
        
        self.result_text = tk.Text(result_frame, font=self.font, height=10, width=80)
        self.result_text.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        
        # رسالة ترحيب
        self.result_text.insert(tk.END, "مرحباً بك في اختبار الأزرار!\n")
        self.result_text.insert(tk.END, "انقر على أي زر لاختبار وظيفته.\n\n")
    
    def save_sample(self):
        self.result_text.insert(tk.END, "✅ تم النقر على زر 'حفظ العينة'\n")
        messagebox.showinfo("حفظ العينة", "تم حفظ العينة بنجاح!")
    
    def edit_sample(self):
        self.result_text.insert(tk.END, "✏️ تم النقر على زر 'تعديل'\n")
        messagebox.showinfo("تعديل", "تم فتح نموذج التعديل")
    
    def delete_sample(self):
        self.result_text.insert(tk.END, "🗑️ تم النقر على زر 'حذف'\n")
        if messagebox.askyesno("حذف", "هل أنت متأكد من الحذف؟"):
            messagebox.showinfo("حذف", "تم الحذف بنجاح")
    
    def new_sample(self):
        self.result_text.insert(tk.END, "🆕 تم النقر على زر 'جديد'\n")
        messagebox.showinfo("جديد", "تم مسح النموذج")
    
    def print_sticker(self):
        self.result_text.insert(tk.END, "🏷️ تم النقر على زر 'طباعة استيكر'\n")
        messagebox.showinfo("طباعة", "تم إرسال الاستيكر للطباعة")
    
    def import_csv(self):
        self.result_text.insert(tk.END, "📥 تم النقر على زر 'استيراد من CSV'\n")
        messagebox.showinfo("استيراد", "تم فتح نافذة اختيار ملف CSV")
    
    def export_csv(self):
        self.result_text.insert(tk.END, "📤 تم النقر على زر 'تصدير إلى CSV'\n")
        messagebox.showinfo("تصدير", "تم تصدير البيانات إلى CSV")
    
    def refresh_data(self):
        self.result_text.insert(tk.END, "🔄 تم النقر على زر 'تحديث القوائم'\n")
        messagebox.showinfo("تحديث", "تم تحديث جميع البيانات")
    
    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

if __name__ == "__main__":
    app = TestButtonsApp()
    app.run()
