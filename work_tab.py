import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont
from datetime import datetime, date
from typing import List, Dict

class WorkTab:
    def __init__(self, parent, db, main_window):
        self.parent = parent
        self.db = db
        self.main_window = main_window
        self.frame = None
        self.current_batch_id = None

        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=10, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=12, weight="bold")

        # الألوان
        self.colors = main_window.colors

        self.create_widgets()

    def create_widgets(self):
        """إنشاء عناصر واجهة العمل"""
        self.frame = tk.Frame(self.parent, bg=self.colors['surface'])

        # عنوان التبويب
        title_label = tk.Label(self.frame,
                              text="إدارة الوجبات والعمل",
                              font=self.header_font,
                              bg=self.colors['surface'],
                              fg=self.colors['text'])
        title_label.pack(pady=10)

        # إطار رئيسي للمحتوى
        main_content = tk.Frame(self.frame, bg=self.colors['surface'])
        main_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # إطار البحث عن العينات
        search_frame = tk.LabelFrame(main_content,
                                    text="البحث عن العينات",
                                    font=self.font,
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'])
        search_frame.pack(fill=tk.X, pady=10)

        self.create_search_section(search_frame)

        # إطار إنشاء الوجبة
        batch_frame = tk.LabelFrame(main_content,
                                   text="إنشاء وإدارة الوجبة",
                                   font=self.font,
                                   bg=self.colors['surface'],
                                   fg=self.colors['text'])
        batch_frame.pack(fill=tk.X, pady=10)

        self.create_batch_section(batch_frame)

        # إطار عرض العينات والوجبات
        display_frame = tk.LabelFrame(main_content,
                                     text="العينات والوجبات",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'])
        display_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        self.create_display_section(display_frame)

    def create_search_section(self, parent):
        """إنشاء قسم البحث عن العينات"""
        # إطار التواريخ
        dates_frame = tk.Frame(parent, bg=self.colors['surface'])
        dates_frame.pack(fill=tk.X, padx=10, pady=10)

        # من تاريخ
        tk.Label(dates_frame, text="من تاريخ:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.from_date_var = tk.StringVar()
        self.from_date_var.set(date.today().strftime('%Y-%m-%d'))
        from_date_entry = tk.Entry(dates_frame, textvariable=self.from_date_var,
                                  font=self.font, width=12)
        from_date_entry.pack(side=tk.LEFT, padx=5)

        # إلى تاريخ
        tk.Label(dates_frame, text="إلى تاريخ:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.to_date_var = tk.StringVar()
        self.to_date_var.set(date.today().strftime('%Y-%m-%d'))
        to_date_entry = tk.Entry(dates_frame, textvariable=self.to_date_var,
                                font=self.font, width=12)
        to_date_entry.pack(side=tk.LEFT, padx=5)

        # زر البحث
        search_btn = tk.Button(dates_frame, text="بحث عن العينات", font=self.font,
                              bg=self.colors['primary'], fg='white', width=15, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.search_samples)
        search_btn.pack(side=tk.LEFT, padx=20)

        # فلاتر إضافية
        filters_frame = tk.Frame(parent, bg=self.colors['surface'])
        filters_frame.pack(fill=tk.X, padx=10, pady=5)

        # فلتر نوع العينة
        tk.Label(filters_frame, text="نوع العينة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.sample_type_filter = ttk.Combobox(filters_frame, font=self.font, width=15, state='readonly')
        self.sample_type_filter.pack(side=tk.LEFT, padx=5)

        # فلتر جهة الإرسال
        tk.Label(filters_frame, text="جهة الإرسال:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.sender_filter = ttk.Combobox(filters_frame, font=self.font, width=20, state='readonly')
        self.sender_filter.pack(side=tk.LEFT, padx=5)

        # تحميل قيم الفلاتر
        self.load_filter_values()

    def create_batch_section(self, parent):
        """إنشاء قسم إدارة الوجبة"""
        # إطار معلومات الوجبة
        batch_info_frame = tk.Frame(parent, bg=self.colors['surface'])
        batch_info_frame.pack(fill=tk.X, padx=10, pady=10)

        # رقم الوجبة
        tk.Label(batch_info_frame, text="رقم الوجبة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.batch_number_var = tk.StringVar()
        batch_number_entry = tk.Entry(batch_info_frame, textvariable=self.batch_number_var,
                                     font=self.font, width=10, state='readonly')
        batch_number_entry.pack(side=tk.LEFT, padx=5)

        # تاريخ العمل
        tk.Label(batch_info_frame, text="تاريخ العمل:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.work_date_var = tk.StringVar()
        self.work_date_var.set(date.today().strftime('%Y-%m-%d'))
        work_date_entry = tk.Entry(batch_info_frame, textvariable=self.work_date_var,
                                  font=self.font, width=12, state='readonly')
        work_date_entry.pack(side=tk.LEFT, padx=5)

        # أزرار الوجبة
        batch_buttons_frame = tk.Frame(parent, bg=self.colors['surface'])
        batch_buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        create_batch_btn = tk.Button(batch_buttons_frame, text="إنشاء وجبة جديدة", font=self.font,
                                    bg=self.colors['primary'], fg='white', width=15, height=2,
                                    relief=tk.RAISED, borderwidth=3,
                                    command=self.create_new_batch)
        create_batch_btn.pack(side=tk.LEFT, padx=5)

        add_samples_btn = tk.Button(batch_buttons_frame, text="إضافة العينات المحددة", font=self.font,
                                   bg=self.colors['secondary'], fg='white', width=18, height=2,
                                   relief=tk.RAISED, borderwidth=3,
                                   command=self.add_samples_to_batch)
        add_samples_btn.pack(side=tk.LEFT, padx=5)

        complete_batch_btn = tk.Button(batch_buttons_frame, text="إكمال الوجبة", font=self.font,
                                      bg=self.colors['success'], fg='white', width=12, height=2,
                                      relief=tk.RAISED, borderwidth=3,
                                      command=self.complete_batch)
        complete_batch_btn.pack(side=tk.LEFT, padx=5)

        send_to_results_btn = tk.Button(batch_buttons_frame, text="إرسال للنتائج", font=self.font,
                                       bg=self.colors['accent'], fg='white', width=12, height=2,
                                       relief=tk.RAISED, borderwidth=3,
                                       command=self.send_to_results)
        send_to_results_btn.pack(side=tk.LEFT, padx=5)

    def create_display_section(self, parent):
        """إنشاء قسم عرض العينات والوجبات"""
        # إطار التبويبات
        notebook = ttk.Notebook(parent)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # تبويب العينات المتاحة
        samples_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(samples_frame, text="العينات المتاحة")

        # جدول العينات المتاحة
        samples_columns = ('اختيار', 'الرقم الوطني', 'الاسم', 'نوع العينة', 'التحليل', 'جهة الإرسال', 'تاريخ السحب')
        self.samples_tree = ttk.Treeview(samples_frame, columns=samples_columns, show='headings', height=12)

        # إعداد العناوين
        for col in samples_columns:
            self.samples_tree.heading(col, text=col)
            if col == 'اختيار':
                self.samples_tree.column(col, width=60, anchor='center')
            else:
                self.samples_tree.column(col, width=120, anchor='center')

        # شريط التمرير للعينات
        samples_scrollbar = ttk.Scrollbar(samples_frame, orient=tk.VERTICAL, command=self.samples_tree.yview)
        self.samples_tree.configure(yscrollcommand=samples_scrollbar.set)

        self.samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        samples_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # تبويب الوجبة الحالية
        current_batch_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(current_batch_frame, text="الوجبة الحالية")

        # جدول الوجبة الحالية
        batch_columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'التحليل', 'الفني المسؤول', 'الحالة')
        self.batch_tree = ttk.Treeview(current_batch_frame, columns=batch_columns, show='headings', height=12)

        # إعداد العناوين
        for col in batch_columns:
            self.batch_tree.heading(col, text=col)
            self.batch_tree.column(col, width=120, anchor='center')

        # شريط التمرير للوجبة
        batch_scrollbar = ttk.Scrollbar(current_batch_frame, orient=tk.VERTICAL, command=self.batch_tree.yview)
        self.batch_tree.configure(yscrollcommand=batch_scrollbar.set)

        self.batch_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        batch_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار تعيين الفنيين
        technician_frame = tk.Frame(current_batch_frame, bg=self.colors['surface'])
        technician_frame.pack(fill=tk.X, pady=10)

        tk.Label(technician_frame, text="تعيين فني:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.technician_var = tk.StringVar()
        self.technician_combo = ttk.Combobox(technician_frame, textvariable=self.technician_var,
                                            font=self.font, width=20, state='readonly')
        self.technician_combo.pack(side=tk.LEFT, padx=5)

        assign_btn = tk.Button(technician_frame, text="تعيين للعينات المحددة", font=self.font,
                              bg=self.colors['primary'], fg='white',
                              command=self.assign_technician)
        assign_btn.pack(side=tk.LEFT, padx=10)

        # تحميل الفنيين
        self.load_technicians()

        # تبويب جميع الوجبات
        all_batches_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(all_batches_frame, text="جميع الوجبات")

        # جدول جميع الوجبات
        all_batches_columns = ('رقم الوجبة', 'تاريخ العمل', 'عدد العينات', 'الحالة', 'تاريخ الإنشاء')
        self.all_batches_tree = ttk.Treeview(all_batches_frame, columns=all_batches_columns, show='headings', height=12)

        # إعداد العناوين
        for col in all_batches_columns:
            self.all_batches_tree.heading(col, text=col)
            self.all_batches_tree.column(col, width=150, anchor='center')

        # شريط التمرير لجميع الوجبات
        all_batches_scrollbar = ttk.Scrollbar(all_batches_frame, orient=tk.VERTICAL, command=self.all_batches_tree.yview)
        self.all_batches_tree.configure(yscrollcommand=all_batches_scrollbar.set)

        self.all_batches_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        all_batches_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # ربط النقر المزدوج لتحميل الوجبة
        self.all_batches_tree.bind('<Double-1>', self.load_selected_batch)

        # تحديث رقم الوجبة التالي
        self.update_next_batch_number()

    def load_filter_values(self):
        """تحميل قيم الفلاتر"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        # تحميل أنواع العينات
        cursor.execute('SELECT DISTINCT name FROM sample_types ORDER BY name')
        sample_types = ['الكل'] + [row[0] for row in cursor.fetchall()]
        self.sample_type_filter['values'] = sample_types
        self.sample_type_filter.set('الكل')

        # تحميل جهات الإرسال
        cursor.execute('SELECT DISTINCT name FROM sender_organizations ORDER BY name')
        senders = ['الكل'] + [row[0] for row in cursor.fetchall()]
        self.sender_filter['values'] = senders
        self.sender_filter.set('الكل')

        conn.close()

    def load_technicians(self):
        """تحميل قائمة الفنيين"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM technicians ORDER BY name')
        technicians = [row[0] for row in cursor.fetchall()]
        self.technician_combo['values'] = technicians
        conn.close()

    def update_next_batch_number(self):
        """تحديث رقم الوجبة التالي"""
        next_batch = self.db.get_next_batch_number()
        self.batch_number_var.set(str(next_batch))

    def search_samples(self):
        """البحث عن العينات حسب التاريخ والفلاتر"""
        from_date = self.from_date_var.get()
        to_date = self.to_date_var.get()
        sample_type_filter = self.sample_type_filter.get()
        sender_filter = self.sender_filter.get()

        conn = self.db.get_connection()
        cursor = conn.cursor()

        # بناء الاستعلام
        query = '''
            SELECT p.id, p.national_id, p.name, p.sample_type, t.name as test_name,
                   p.sender_organization, p.sample_collection_date
            FROM patients p
            JOIN patient_tests pt ON p.id = pt.patient_id
            JOIN tests t ON pt.test_id = t.id
            WHERE p.sample_collection_date BETWEEN ? AND ?
        '''
        params = [from_date, to_date]

        # إضافة فلاتر إضافية
        if sample_type_filter != 'الكل':
            query += ' AND p.sample_type = ?'
            params.append(sample_type_filter)

        if sender_filter != 'الكل':
            query += ' AND p.sender_organization = ?'
            params.append(sender_filter)

        # استبعاد العينات الموجودة في وجبات مكتملة
        query += '''
            AND p.id NOT IN (
                SELECT bs.patient_id FROM batch_samples bs
                JOIN batches b ON bs.batch_id = b.id
                WHERE b.status IN ('مكتمل', 'مرسل للنتائج')
            )
        '''

        query += ' ORDER BY p.sample_collection_date, p.national_id'

        cursor.execute(query, params)
        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.samples_tree.get_children():
            self.samples_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            patient_id, national_id, name, sample_type, test_name, sender_org, collection_date = row
            self.samples_tree.insert('', tk.END, values=('☐', national_id, name, sample_type, test_name, sender_org, collection_date))

        # ربط النقر لتحديد العينات
        self.samples_tree.bind('<Button-1>', self.toggle_sample_selection)

    def toggle_sample_selection(self, event):
        """تبديل تحديد العينة"""
        item = self.samples_tree.identify('item', event.x, event.y)
        if not item:
            return

        column = self.samples_tree.identify('column', event.x, event.y)
        if column == '#1':  # عمود الاختيار
            values = list(self.samples_tree.item(item, 'values'))
            if values[0] == '☐':
                values[0] = '☑'
            else:
                values[0] = '☐'
            self.samples_tree.item(item, values=values)

    def create_new_batch(self):
        """إنشاء وجبة جديدة"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            batch_number = int(self.batch_number_var.get())
            work_date = self.work_date_var.get()

            cursor.execute('''
                INSERT INTO batches (batch_number, work_date, status)
                VALUES (?, ?, 'في العمل')
            ''', (batch_number, work_date))

            self.current_batch_id = cursor.lastrowid

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم إنشاء الوجبة رقم {batch_number} بنجاح")

            # تحديث رقم الوجبة التالي
            self.update_next_batch_number()

            # تحديث عرض الوجبات
            self.load_all_batches()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الوجبة: {str(e)}")

    def add_samples_to_batch(self):
        """إضافة العينات المحددة إلى الوجبة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "يرجى إنشاء وجبة أولاً")
            return

        # جمع العينات المحددة
        selected_samples = []
        for item in self.samples_tree.get_children():
            values = self.samples_tree.item(item, 'values')
            if values[0] == '☑':  # محددة
                selected_samples.append({
                    'national_id': values[1],
                    'name': values[2],
                    'sample_type': values[3],
                    'test_name': values[4]
                })

        if not selected_samples:
            messagebox.showwarning("تحذير", "يرجى تحديد عينات لإضافتها")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            added_count = 0

            for sample in selected_samples:
                # الحصول على معرف المريض والتحليل
                cursor.execute('''
                    SELECT p.id, t.id FROM patients p
                    JOIN patient_tests pt ON p.id = pt.patient_id
                    JOIN tests t ON pt.test_id = t.id
                    WHERE p.national_id = ? AND t.name = ?
                ''', (sample['national_id'], sample['test_name']))

                result = cursor.fetchone()
                if result:
                    patient_id, test_id = result

                    # التحقق من عدم وجود العينة في الوجبة
                    cursor.execute('''
                        SELECT id FROM batch_samples
                        WHERE batch_id = ? AND patient_id = ? AND test_id = ?
                    ''', (self.current_batch_id, patient_id, test_id))

                    if not cursor.fetchone():
                        cursor.execute('''
                            INSERT INTO batch_samples (batch_id, patient_id, test_id)
                            VALUES (?, ?, ?)
                        ''', (self.current_batch_id, patient_id, test_id))
                        added_count += 1

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم إضافة {added_count} عينة إلى الوجبة")

            # تحديث عرض الوجبة الحالية
            self.load_current_batch()

            # إزالة العينات المضافة من قائمة العينات المتاحة
            self.search_samples()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة العينات: {str(e)}")

    def load_current_batch(self):
        """تحميل الوجبة الحالية"""
        if not self.current_batch_id:
            return

        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT p.national_id, p.name, p.sample_type, t.name as test_name,
                   bs.technician_name, 'في الانتظار' as status
            FROM batch_samples bs
            JOIN patients p ON bs.patient_id = p.id
            JOIN tests t ON bs.test_id = t.id
            WHERE bs.batch_id = ?
            ORDER BY p.national_id
        ''', (self.current_batch_id,))

        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.batch_tree.get_children():
            self.batch_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            national_id, name, sample_type, test_name, technician, status = row
            technician_display = technician or 'غير محدد'
            self.batch_tree.insert('', tk.END, values=(national_id, name, sample_type, test_name, technician_display, status))

    def assign_technician(self):
        """تعيين فني للعينات المحددة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "لا توجد وجبة نشطة")
            return

        technician = self.technician_var.get()
        if not technician:
            messagebox.showwarning("تحذير", "يرجى اختيار فني")
            return

        # جمع العينات المحددة
        selected_items = self.batch_tree.selection()
        if not selected_items:
            messagebox.showwarning("تحذير", "يرجى تحديد عينات لتعيين الفني")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            updated_count = 0

            for item in selected_items:
                values = self.batch_tree.item(item, 'values')
                national_id = values[0]
                test_name = values[3]

                # تحديث الفني المسؤول
                cursor.execute('''
                    UPDATE batch_samples
                    SET technician_name = ?
                    WHERE batch_id = ? AND patient_id = (
                        SELECT id FROM patients WHERE national_id = ?
                    ) AND test_id = (
                        SELECT id FROM tests WHERE name = ?
                    )
                ''', (technician, self.current_batch_id, national_id, test_name))

                updated_count += 1

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", f"تم تعيين الفني {technician} لـ {updated_count} عينة")

            # تحديث عرض الوجبة الحالية
            self.load_current_batch()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعيين الفني: {str(e)}")

    def complete_batch(self):
        """إكمال الوجبة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "لا توجد وجبة نشطة")
            return

        # التحقق من تعيين فنيين لجميع العينات
        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT COUNT(*) FROM batch_samples
            WHERE batch_id = ? AND (technician_name IS NULL OR technician_name = '')
        ''', (self.current_batch_id,))

        unassigned_count = cursor.fetchone()[0]

        if unassigned_count > 0:
            if not messagebox.askyesno("تأكيد", f"يوجد {unassigned_count} عينة لم يتم تعيين فني لها. هل تريد المتابعة؟"):
                conn.close()
                return

        try:
            # تحديث حالة الوجبة
            cursor.execute('''
                UPDATE batches SET status = 'مكتمل' WHERE id = ?
            ''', (self.current_batch_id,))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إكمال الوجبة بنجاح")

            # إعادة تعيين الوجبة الحالية
            self.current_batch_id = None

            # تحديث العرض
            self.load_all_batches()

            # مسح عرض الوجبة الحالية
            for item in self.batch_tree.get_children():
                self.batch_tree.delete(item)

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إكمال الوجبة: {str(e)}")

    def send_to_results(self):
        """إرسال الوجبة إلى تبويب النتائج"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "لا توجد وجبة نشطة")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # التحقق من حالة الوجبة
            cursor.execute('SELECT status FROM batches WHERE id = ?', (self.current_batch_id,))
            batch_status = cursor.fetchone()[0]

            if batch_status != 'مكتمل':
                messagebox.showwarning("تحذير", "يجب إكمال الوجبة أولاً قبل إرسالها للنتائج")
                conn.close()
                return

            # تحديث حالة الوجبة
            cursor.execute('''
                UPDATE batches SET status = 'مرسل للنتائج' WHERE id = ?
            ''', (self.current_batch_id,))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إرسال الوجبة إلى تبويب النتائج بنجاح")

            # إعادة تعيين الوجبة الحالية
            self.current_batch_id = None

            # تحديث العرض
            self.load_all_batches()

            # مسح عرض الوجبة الحالية
            for item in self.batch_tree.get_children():
                self.batch_tree.delete(item)

            # تحديث تبويب النتائج
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إرسال الوجبة: {str(e)}")

    def load_all_batches(self):
        """تحميل جميع الوجبات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT b.batch_number, b.work_date,
                   COUNT(bs.id) as sample_count,
                   b.status, b.created_at
            FROM batches b
            LEFT JOIN batch_samples bs ON b.id = bs.batch_id
            GROUP BY b.id
            ORDER BY b.batch_number DESC
        ''')

        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.all_batches_tree.get_children():
            self.all_batches_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            batch_number, work_date, sample_count, status, created_at = row
            self.all_batches_tree.insert('', tk.END, values=(batch_number, work_date, sample_count, status, created_at))

    def load_selected_batch(self, event):
        """تحميل الوجبة المحددة"""
        selection = self.all_batches_tree.selection()
        if not selection:
            return

        item = self.all_batches_tree.item(selection[0])
        batch_number = item['values'][0]

        # جلب معرف الوجبة
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, status FROM batches WHERE batch_number = ?', (batch_number,))
        result = cursor.fetchone()
        conn.close()

        if result:
            batch_id, status = result
            if status == 'في العمل':
                self.current_batch_id = batch_id
                self.batch_number_var.set(str(batch_number))
                self.load_current_batch()
                messagebox.showinfo("تم التحميل", f"تم تحميل الوجبة رقم {batch_number}")
            else:
                messagebox.showinfo("معلومات", f"الوجبة رقم {batch_number} في حالة: {status}")

    def show(self):
        """عرض التبويب"""
        self.frame.pack(fill=tk.BOTH, expand=True)
        # تحديث البيانات عند العرض
        self.load_all_batches()

    def hide(self):
        """إخفاء التبويب"""
        self.frame.pack_forget()

    def refresh(self):
        """تحديث التبويب"""
        self.load_filter_values()
        self.load_technicians()
        self.load_all_batches()
        if self.current_batch_id:
            self.load_current_batch()