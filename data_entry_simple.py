#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from datetime import datetime, date
import csv

class DataEntryTab:
    def __init__(self, parent, db, main_window):
        self.parent = parent
        self.db = db
        self.main_window = main_window
        
        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=12, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=14, weight="bold")
        
        # الألوان
        self.colors = main_window.colors
        
        self.create_widgets()
        self.update_auto_fields()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة إدخال البيانات"""
        self.frame = tk.Frame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = tk.Label(self.frame,
                              text="إدخال بيانات المرضى والعينات",
                              font=self.header_font,
                              bg=self.colors['surface'],
                              fg=self.colors['text'])
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.LabelFrame(self.frame,
                                  text="بيانات المريض والعينة",
                                  font=self.font,
                                  bg=self.colors['surface'],
                                  fg=self.colors['text'])
        form_frame.pack(fill=tk.X, pady=10, padx=20)
        
        # إنشاء حقول بسيطة
        self.create_simple_form(form_frame)
        
        # إطار الأزرار - مع خلفية مميزة
        buttons_container = tk.LabelFrame(self.frame,
                                         text="العمليات",
                                         font=self.font,
                                         bg=self.colors['surface'],
                                         fg=self.colors['text'],
                                         relief=tk.RAISED,
                                         borderwidth=3)
        buttons_container.pack(fill=tk.X, pady=20, padx=20)
        
        self.create_buttons(buttons_container)
        
        # منطقة النتائج
        result_frame = tk.LabelFrame(self.frame,
                                    text="النتائج",
                                    font=self.font,
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'])
        result_frame.pack(fill=tk.BOTH, expand=True, pady=10, padx=20)
        
        self.result_text = tk.Text(result_frame, font=self.font, height=8)
        self.result_text.pack(pady=10, padx=10, fill=tk.BOTH, expand=True)
        self.result_text.insert(tk.END, "مرحباً بك في نظام إدخال البيانات!\n")
        self.result_text.insert(tk.END, "املأ البيانات واستخدم الأزرار أدناه.\n\n")
    
    def create_simple_form(self, parent):
        """إنشاء نموذج بسيط"""
        self.form_vars = {}
        
        # إطار للحقول
        fields_frame = tk.Frame(parent, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=20, pady=20)
        
        # الحقول الأساسية
        fields = [
            ('national_id', 'الرقم الوطني'),
            ('name', 'الاسم *'),
            ('age', 'العمر *'),
            ('phone', 'رقم الهاتف *')
        ]
        
        for field_id, label_text in fields:
            field_frame = tk.Frame(fields_frame, bg=self.colors['surface'])
            field_frame.pack(fill=tk.X, pady=5)
            
            label = tk.Label(field_frame, text=label_text, font=self.font,
                            bg=self.colors['surface'], fg=self.colors['text'],
                            width=15, anchor='w')
            label.pack(side=tk.LEFT)
            
            self.form_vars[field_id] = tk.StringVar()
            entry = tk.Entry(field_frame, textvariable=self.form_vars[field_id],
                            font=self.font, width=30)
            entry.pack(side=tk.LEFT, padx=10)
    
    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        
        # عنوان فرعي للأزرار الرئيسية
        main_title = tk.Label(parent, text="العمليات الأساسية:", 
                             font=self.font, bg=self.colors['surface'], 
                             fg=self.colors['text'])
        main_title.pack(pady=(15, 10))
        
        # الصف الأول - أزرار العمليات الأساسية
        main_buttons_frame = tk.Frame(parent, bg=self.colors['surface'])
        main_buttons_frame.pack(pady=15)
        
        main_buttons = [
            ('💾 حفظ العينة', self.colors['primary'], self.save_sample),
            ('✏️ تعديل', self.colors['secondary'], self.edit_sample),
            ('🗑️ حذف', self.colors['success'], self.delete_sample),
            ('🆕 جديد', self.colors['accent'], self.new_sample),
            ('🏷️ طباعة استيكر', self.colors['text'], self.print_sticker)
        ]
        
        for text, color, command in main_buttons:
            btn = tk.Button(main_buttons_frame, text=text, font=self.font,
                           bg=color, fg='white', width=15, height=3,
                           relief=tk.RAISED, borderwidth=3,
                           command=command)
            btn.pack(side=tk.LEFT, padx=10)
        
        # عنوان فرعي للاستيراد والتصدير
        import_title = tk.Label(parent, text="الاستيراد والتصدير:", 
                               font=self.font, bg=self.colors['surface'], 
                               fg=self.colors['text'])
        import_title.pack(pady=(25, 10))
        
        # الصف الثاني - أزرار الاستيراد والتصدير
        import_export_frame = tk.Frame(parent, bg=self.colors['surface'])
        import_export_frame.pack(pady=15)
        
        import_export_buttons = [
            ('📥 استيراد من CSV', self.colors['primary'], self.import_csv),
            ('📤 تصدير إلى CSV', self.colors['secondary'], self.export_csv),
            ('🔄 تحديث القوائم', self.colors['accent'], self.refresh_data)
        ]
        
        for text, color, command in import_export_buttons:
            btn = tk.Button(import_export_frame, text=text, font=self.font,
                           bg=color, fg='white', width=18, height=3,
                           relief=tk.RAISED, borderwidth=3,
                           command=command)
            btn.pack(side=tk.LEFT, padx=10)
        
        # مساحة إضافية في الأسفل
        spacer = tk.Frame(parent, bg=self.colors['surface'], height=20)
        spacer.pack()
    
    def update_auto_fields(self):
        """تحديث الحقول التلقائية"""
        try:
            next_id = self.db.get_next_national_id()
            self.form_vars['national_id'].set(str(next_id))
        except:
            self.form_vars['national_id'].set("001")
    
    def save_sample(self):
        self.result_text.insert(tk.END, "✅ تم النقر على زر 'حفظ العينة'\n")
        messagebox.showinfo("حفظ العينة", "تم حفظ العينة بنجاح!")
    
    def edit_sample(self):
        self.result_text.insert(tk.END, "✏️ تم النقر على زر 'تعديل'\n")
        messagebox.showinfo("تعديل", "تم فتح نموذج التعديل")
    
    def delete_sample(self):
        self.result_text.insert(tk.END, "🗑️ تم النقر على زر 'حذف'\n")
        if messagebox.askyesno("حذف", "هل أنت متأكد من الحذف؟"):
            messagebox.showinfo("حذف", "تم الحذف بنجاح")
    
    def new_sample(self):
        self.result_text.insert(tk.END, "🆕 تم النقر على زر 'جديد'\n")
        for var in self.form_vars.values():
            var.set("")
        self.update_auto_fields()
        messagebox.showinfo("جديد", "تم مسح النموذج")
    
    def print_sticker(self):
        self.result_text.insert(tk.END, "🏷️ تم النقر على زر 'طباعة استيكر'\n")
        messagebox.showinfo("طباعة", "تم إرسال الاستيكر للطباعة")
    
    def import_csv(self):
        self.result_text.insert(tk.END, "📥 تم النقر على زر 'استيراد من CSV'\n")
        filename = filedialog.askopenfilename(
            title="اختر ملف CSV",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            messagebox.showinfo("استيراد", f"تم اختيار الملف: {filename}")
    
    def export_csv(self):
        self.result_text.insert(tk.END, "📤 تم النقر على زر 'تصدير إلى CSV'\n")
        filename = filedialog.asksaveasfilename(
            title="حفظ ملف CSV",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")]
        )
        if filename:
            messagebox.showinfo("تصدير", f"تم حفظ الملف: {filename}")
    
    def refresh_data(self):
        self.result_text.insert(tk.END, "🔄 تم النقر على زر 'تحديث القوائم'\n")
        self.update_auto_fields()
        messagebox.showinfo("تحديث", "تم تحديث جميع البيانات")
    
    def show(self):
        """عرض التبويب"""
        self.frame.pack(fill=tk.BOTH, expand=True)
    
    def hide(self):
        """إخفاء التبويب"""
        self.frame.pack_forget()
    
    def refresh(self):
        """تحديث التبويب"""
        self.update_auto_fields()
