#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont
from datetime import datetime

class ResultsTab:
    def __init__(self, parent, db, main_window):
        self.parent = parent
        self.db = db
        self.main_window = main_window
        self.frame = None
        self.current_batch_id = None
        self.selected_sample_id = None
        
        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=12, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=14, weight="bold")
        
        # الألوان
        self.colors = main_window.colors
        
        # خيارات النتائج
        self.result_options = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة النتائج"""
        self.frame = tk.Frame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = tk.Label(self.frame,
                              text="إدخال نتائج التحاليل",
                              font=self.header_font,
                              bg=self.colors['surface'],
                              fg=self.colors['text'])
        title_label.pack(pady=10)
        
        # إطار رئيسي للمحتوى
        main_content = tk.Frame(self.frame, bg=self.colors['surface'])
        main_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إطار اختيار الوجبة
        batch_frame = tk.LabelFrame(main_content,
                                   text="اختيار الوجبة",
                                   font=self.font,
                                   bg=self.colors['surface'],
                                   fg=self.colors['text'])
        batch_frame.pack(fill=tk.X, pady=10)
        
        self.create_batch_selection(batch_frame)
        
        # إطار عرض العينات
        samples_frame = tk.LabelFrame(main_content,
                                     text="العينات المرسلة",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'])
        samples_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.create_samples_display(samples_frame)
        
        # إطار إدخال النتائج
        results_frame = tk.LabelFrame(main_content,
                                     text="إدخال النتيجة",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'])
        results_frame.pack(fill=tk.X, pady=10)
        
        self.create_results_entry(results_frame)
    
    def create_batch_selection(self, parent):
        """إنشاء قسم اختيار الوجبة"""
        # إطار البحث
        search_frame = tk.Frame(parent, bg=self.colors['surface'])
        search_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # رقم الوجبة
        tk.Label(search_frame, text="رقم الوجبة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.batch_number_var = tk.StringVar()
        batch_entry = tk.Entry(search_frame, textvariable=self.batch_number_var,
                              font=self.font, width=15)
        batch_entry.pack(side=tk.LEFT, padx=5)
        
        # زر البحث
        search_btn = tk.Button(search_frame, text="🔍 عرض الوجبة", font=self.font,
                              bg=self.colors['primary'], fg='white', width=15, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.load_batch)
        search_btn.pack(side=tk.LEFT, padx=10)
        
        # زر تحديث
        refresh_btn = tk.Button(search_frame, text="🔄 تحديث", font=self.font,
                               bg=self.colors['accent'], fg='white', width=10, height=2,
                               relief=tk.RAISED, borderwidth=3,
                               command=self.refresh_batches)
        refresh_btn.pack(side=tk.LEFT, padx=5)
        
        # قائمة الوجبات المتاحة
        batches_frame = tk.Frame(parent, bg=self.colors['surface'])
        batches_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(batches_frame, text="الوجبات المتاحة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(anchor='w')
        
        # قائمة الوجبات
        self.batches_listbox = tk.Listbox(batches_frame, font=self.font, height=4)
        self.batches_listbox.pack(fill=tk.X, pady=5)
        self.batches_listbox.bind('<Double-Button-1>', self.on_batch_select)
        
        # تحميل الوجبات
        self.load_available_batches()
    
    def create_samples_display(self, parent):
        """إنشاء قسم عرض العينات"""
        # جدول العينات
        columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'التحاليل', 'الحالة')
        self.samples_tree = ttk.Treeview(parent, columns=columns, show='headings', height=8)
        
        # تعيين عناوين الأعمدة
        for col in columns:
            self.samples_tree.heading(col, text=col)
            self.samples_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.samples_tree.yview)
        self.samples_tree.configure(yscrollcommand=scrollbar.set)
        
        self.samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # ربط حدث التحديد
        self.samples_tree.bind('<<TreeviewSelect>>', self.on_sample_select)
    
    def create_results_entry(self, parent):
        """إنشاء قسم إدخال النتائج"""
        # معلومات العينة المحددة
        info_frame = tk.Frame(parent, bg=self.colors['surface'])
        info_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.sample_info_var = tk.StringVar()
        self.sample_info_var.set("يرجى اختيار عينة من الجدول أعلاه")
        info_label = tk.Label(info_frame, textvariable=self.sample_info_var, font=self.font,
                             bg=self.colors['surface'], fg=self.colors['text'])
        info_label.pack(anchor='w')
        
        # إطار اختيار النتيجة
        result_frame = tk.Frame(parent, bg=self.colors['surface'])
        result_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(result_frame, text="النتيجة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.result_var = tk.StringVar()
        result_combo = ttk.Combobox(result_frame, textvariable=self.result_var,
                                   values=self.result_options, font=self.font,
                                   width=15, state='readonly')
        result_combo.pack(side=tk.LEFT, padx=10)
        
        # ملاحظات
        tk.Label(result_frame, text="ملاحظات:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 5))
        
        self.notes_var = tk.StringVar()
        notes_entry = tk.Entry(result_frame, textvariable=self.notes_var,
                              font=self.font, width=30)
        notes_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(parent, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=20)
        
        # زر حفظ النتيجة
        save_btn = tk.Button(buttons_frame, text="💾 حفظ النتيجة", font=self.font,
                            bg=self.colors['primary'], fg='white', width=15, height=3,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.save_result)
        save_btn.pack(side=tk.LEFT, padx=10)
        
        # زر تعديل النتيجة
        edit_btn = tk.Button(buttons_frame, text="✏️ تعديل النتيجة", font=self.font,
                            bg=self.colors['secondary'], fg='white', width=15, height=3,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.edit_result)
        edit_btn.pack(side=tk.LEFT, padx=10)
        
        # زر حذف النتيجة
        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف النتيجة", font=self.font,
                              bg=self.colors['success'], fg='white', width=15, height=3,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.delete_result)
        delete_btn.pack(side=tk.LEFT, padx=10)
        
        # زر مسح الحقول
        clear_btn = tk.Button(buttons_frame, text="🆕 مسح الحقول", font=self.font,
                             bg=self.colors['accent'], fg='white', width=15, height=3,
                             relief=tk.RAISED, borderwidth=3,
                             command=self.clear_fields)
        clear_btn.pack(side=tk.LEFT, padx=10)

    def load_available_batches(self):
        """تحميل الوجبات المتاحة"""
        try:
            self.batches_listbox.delete(0, tk.END)
            # محاكاة بيانات الوجبات
            sample_batches = [
                "وجبة رقم 001 - 2024-01-15 (5 عينات)",
                "وجبة رقم 002 - 2024-01-16 (8 عينات)",
                "وجبة رقم 003 - 2024-01-17 (12 عينات)",
                "وجبة رقم 004 - 2024-01-18 (6 عينات)"
            ]

            for batch in sample_batches:
                self.batches_listbox.insert(tk.END, batch)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الوجبات: {str(e)}")

    def load_batch(self):
        """تحميل وجبة محددة"""
        batch_number = self.batch_number_var.get().strip()
        if not batch_number:
            messagebox.showwarning("تحذير", "يرجى إدخال رقم الوجبة")
            return

        try:
            # مسح الجدول
            for item in self.samples_tree.get_children():
                self.samples_tree.delete(item)

            # محاكاة بيانات العينات
            sample_data = [
                ("001", "أحمد محمد", "دم", "فحص كوفيد", "في الانتظار"),
                ("002", "فاطمة علي", "مسحة", "فحص كوفيد", "في الانتظار"),
                ("003", "محمد حسن", "دم", "فحص شامل", "مكتملة"),
                ("004", "زينب أحمد", "بول", "فحص بول", "في الانتظار"),
                ("005", "علي محمود", "دم", "فحص سكر", "في الانتظار")
            ]

            for data in sample_data:
                self.samples_tree.insert('', tk.END, values=data)

            messagebox.showinfo("نجح", f"تم تحميل الوجبة رقم {batch_number} بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تحميل الوجبة: {str(e)}")

    def on_batch_select(self, event):
        """عند اختيار وجبة من القائمة"""
        selection = self.batches_listbox.curselection()
        if selection:
            batch_text = self.batches_listbox.get(selection[0])
            # استخراج رقم الوجبة
            batch_number = batch_text.split()[2]  # "وجبة رقم 001"
            self.batch_number_var.set(batch_number)
            self.load_batch()

    def on_sample_select(self, event):
        """عند اختيار عينة من الجدول"""
        selection = self.samples_tree.selection()
        if selection:
            item = self.samples_tree.item(selection[0])
            values = item['values']

            sample_info = f"العينة المحددة: {values[0]} - {values[1]} - {values[2]} - {values[3]}"
            self.sample_info_var.set(sample_info)
            self.selected_sample_id = values[0]

    def save_result(self):
        """حفظ النتيجة"""
        if not self.selected_sample_id:
            messagebox.showwarning("تحذير", "يرجى اختيار عينة أولاً")
            return

        if not self.result_var.get():
            messagebox.showwarning("تحذير", "يرجى اختيار النتيجة")
            return

        try:
            # محاكاة حفظ النتيجة
            messagebox.showinfo("نجح", f"تم حفظ النتيجة '{self.result_var.get()}' للعينة {self.selected_sample_id}")
            self.clear_fields()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ النتيجة: {str(e)}")

    def edit_result(self):
        """تعديل النتيجة"""
        if not self.selected_sample_id:
            messagebox.showwarning("تحذير", "يرجى اختيار عينة أولاً")
            return

        messagebox.showinfo("تعديل", f"تم فتح نموذج تعديل النتيجة للعينة {self.selected_sample_id}")

    def delete_result(self):
        """حذف النتيجة"""
        if not self.selected_sample_id:
            messagebox.showwarning("تحذير", "يرجى اختيار عينة أولاً")
            return

        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف نتيجة العينة {self.selected_sample_id}؟"):
            messagebox.showinfo("حذف", "تم حذف النتيجة بنجاح")
            self.clear_fields()

    def clear_fields(self):
        """مسح الحقول"""
        self.result_var.set("")
        self.notes_var.set("")
        self.sample_info_var.set("يرجى اختيار عينة من الجدول أعلاه")
        self.selected_sample_id = None

    def refresh_batches(self):
        """تحديث قائمة الوجبات"""
        self.load_available_batches()
        messagebox.showinfo("تحديث", "تم تحديث قائمة الوجبات")

    def show(self):
        """عرض التبويب"""
        self.frame.pack(fill=tk.BOTH, expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.frame.pack_forget()

    def refresh(self):
        """تحديث التبويب"""
        self.load_available_batches()
