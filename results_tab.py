import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont
from datetime import datetime
from typing import List, Dict

class ResultsTab:
    def __init__(self, parent, db, main_window):
        self.parent = parent
        self.db = db
        self.main_window = main_window
        self.frame = None
        self.current_batch_id = None
        
        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=10, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=12, weight="bold")
        
        # الألوان
        self.colors = main_window.colors
        
        # خيارات النتائج
        self.result_options = ['Negative', 'Positive', 'Retest', 'Recollection', 'Sent', 'TND']
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة النتائج"""
        self.frame = tk.Frame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = tk.Label(self.frame,
                              text="إدخال وإدارة النتائج",
                              font=self.header_font,
                              bg=self.colors['surface'],
                              fg=self.colors['text'])
        title_label.pack(pady=10)
        
        # إطار رئيسي للمحتوى
        main_content = tk.Frame(self.frame, bg=self.colors['surface'])
        main_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إطار البحث عن الوجبة
        search_frame = tk.LabelFrame(main_content,
                                    text="البحث عن الوجبة",
                                    font=self.font,
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'])
        search_frame.pack(fill=tk.X, pady=10)
        
        self.create_search_section(search_frame)
        
        # إطار إدخال النتائج
        results_frame = tk.LabelFrame(main_content,
                                     text="إدخال النتائج",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'])
        results_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.create_results_section(results_frame)
    
    def create_search_section(self, parent):
        """إنشاء قسم البحث عن الوجبة"""
        # إطار البحث
        search_input_frame = tk.Frame(parent, bg=self.colors['surface'])
        search_input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # رقم الوجبة
        tk.Label(search_input_frame, text="رقم الوجبة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.batch_number_var = tk.StringVar()
        batch_entry = tk.Entry(search_input_frame, textvariable=self.batch_number_var,
                              font=self.font, width=15)
        batch_entry.pack(side=tk.LEFT, padx=5)
        
        # زر البحث
        search_btn = tk.Button(search_input_frame, text="عرض", font=self.font,
                              bg=self.colors['primary'], fg='white', width=10, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.search_batch)
        search_btn.pack(side=tk.LEFT, padx=10)
        
        # معلومات الوجبة
        info_frame = tk.Frame(parent, bg=self.colors['surface'])
        info_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.batch_info_var = tk.StringVar()
        self.batch_info_var.set("لم يتم تحديد وجبة")
        info_label = tk.Label(info_frame, textvariable=self.batch_info_var, font=self.font,
                             bg=self.colors['surface'], fg=self.colors['text'])
        info_label.pack(anchor='w')
        
        # قائمة الوجبات المتاحة
        available_frame = tk.Frame(parent, bg=self.colors['surface'])
        available_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(available_frame, text="الوجبات المتاحة للنتائج:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(anchor='w')
        
        # جدول الوجبات المتاحة
        available_columns = ('رقم الوجبة', 'تاريخ العمل', 'عدد العينات', 'الحالة')
        self.available_batches_tree = ttk.Treeview(available_frame, columns=available_columns, 
                                                  show='headings', height=6)
        
        # إعداد العناوين
        for col in available_columns:
            self.available_batches_tree.heading(col, text=col)
            self.available_batches_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        available_scrollbar = ttk.Scrollbar(available_frame, orient=tk.VERTICAL, 
                                           command=self.available_batches_tree.yview)
        self.available_batches_tree.configure(yscrollcommand=available_scrollbar.set)
        
        self.available_batches_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج
        self.available_batches_tree.bind('<Double-1>', self.select_batch_from_list)
        
        # تحميل الوجبات المتاحة
        self.load_available_batches()
    
    def create_results_section(self, parent):
        """إنشاء قسم إدخال النتائج"""
        # إطار العينات
        samples_frame = tk.Frame(parent, bg=self.colors['surface'])
        samples_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # جدول العينات
        samples_columns = ('الرقم الوطني', 'الاسم', 'نوع العينة', 'التحليل', 'الفني المسؤول', 'النتيجة', 'ملاحظات')
        self.samples_tree = ttk.Treeview(samples_frame, columns=samples_columns, show='headings', height=15)
        
        # إعداد العناوين
        for col in samples_columns:
            self.samples_tree.heading(col, text=col)
            if col in ['النتيجة', 'ملاحظات']:
                self.samples_tree.column(col, width=100, anchor='center')
            else:
                self.samples_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        samples_scrollbar = ttk.Scrollbar(samples_frame, orient=tk.VERTICAL, command=self.samples_tree.yview)
        self.samples_tree.configure(yscrollcommand=samples_scrollbar.set)
        
        self.samples_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        samples_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # إطار إدخال النتيجة
        result_input_frame = tk.Frame(parent, bg=self.colors['surface'])
        result_input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # النتيجة
        tk.Label(result_input_frame, text="النتيجة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.result_var = tk.StringVar()
        result_combo = ttk.Combobox(result_input_frame, textvariable=self.result_var,
                                   values=self.result_options, font=self.font, width=15, state='readonly')
        result_combo.pack(side=tk.LEFT, padx=5)
        
        # ملاحظات
        tk.Label(result_input_frame, text="ملاحظات:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))
        
        self.notes_var = tk.StringVar()
        notes_entry = tk.Entry(result_input_frame, textvariable=self.notes_var,
                              font=self.font, width=30)
        notes_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(parent, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        save_result_btn = tk.Button(buttons_frame, text="حفظ النتيجة", font=self.font,
                                   bg=self.colors['primary'], fg='white', width=12, height=2,
                                   relief=tk.RAISED, borderwidth=3,
                                   command=self.save_result)
        save_result_btn.pack(side=tk.LEFT, padx=5)
        
        save_all_btn = tk.Button(buttons_frame, text="حفظ جميع النتائج", font=self.font,
                                bg=self.colors['secondary'], fg='white', width=15, height=2,
                                relief=tk.RAISED, borderwidth=3,
                                command=self.save_all_results)
        save_all_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(buttons_frame, text="مسح", font=self.font,
                             bg=self.colors['accent'], fg='white', width=10, height=2,
                             relief=tk.RAISED, borderwidth=3,
                             command=self.clear_selection)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # إطار النتائج السريعة
        quick_results_frame = tk.LabelFrame(parent, text="النتائج السريعة", font=self.font,
                                           bg=self.colors['surface'], fg=self.colors['text'])
        quick_results_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # أزرار النتائج السريعة
        quick_buttons_frame = tk.Frame(quick_results_frame, bg=self.colors['surface'])
        quick_buttons_frame.pack(pady=10)
        
        quick_results = [
            ('Negative', self.colors['primary']),
            ('Positive', self.colors['success']),
            ('Retest', self.colors['secondary']),
            ('Recollection', self.colors['accent']),
            ('Sent', self.colors['text']),
            ('TND', '#6C757D')
        ]
        
        for result, color in quick_results:
            btn = tk.Button(quick_buttons_frame, text=result, font=self.font,
                           bg=color, fg='white', width=12, height=2,
                           relief=tk.RAISED, borderwidth=3,
                           command=lambda r=result: self.set_quick_result(r))
            btn.pack(side=tk.LEFT, padx=5)
        
        # ربط تحديد العينة
        self.samples_tree.bind('<<TreeviewSelect>>', self.on_sample_select)
    
    def load_available_batches(self):
        """تحميل الوجبات المتاحة للنتائج"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT b.batch_number, b.work_date, 
                   COUNT(bs.id) as sample_count, 
                   b.status
            FROM batches b
            LEFT JOIN batch_samples bs ON b.id = bs.batch_id
            WHERE b.status IN ('مكتمل', 'مرسل للنتائج')
            GROUP BY b.id
            ORDER BY b.batch_number DESC
        ''')
        
        results = cursor.fetchall()
        conn.close()
        
        # مسح النتائج السابقة
        for item in self.available_batches_tree.get_children():
            self.available_batches_tree.delete(item)
        
        # إضافة النتائج الجديدة
        for row in results:
            batch_number, work_date, sample_count, status = row
            self.available_batches_tree.insert('', tk.END, values=(batch_number, work_date, sample_count, status))
    
    def search_batch(self):
        """البحث عن الوجبة برقمها"""
        batch_number = self.batch_number_var.get().strip()
        
        if not batch_number:
            messagebox.showwarning("تحذير", "يرجى إدخال رقم الوجبة")
            return
        
        try:
            batch_number = int(batch_number)
        except ValueError:
            messagebox.showerror("خطأ", "رقم الوجبة يجب أن يكون رقماً")
            return
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, batch_number, work_date, status FROM batches 
            WHERE batch_number = ?
        ''', (batch_number,))
        
        batch = cursor.fetchone()
        conn.close()
        
        if not batch:
            messagebox.showerror("خطأ", "لم يتم العثور على الوجبة")
            return
        
        batch_id, batch_num, work_date, status = batch
        
        if status not in ['مكتمل', 'مرسل للنتائج']:
            messagebox.showwarning("تحذير", f"الوجبة في حالة: {status}. يجب أن تكون مكتملة أو مرسلة للنتائج")
            return
        
        self.current_batch_id = batch_id
        self.batch_info_var.set(f"الوجبة رقم: {batch_num} | تاريخ العمل: {work_date} | الحالة: {status}")
        
        # تحميل عينات الوجبة
        self.load_batch_samples()
    
    def select_batch_from_list(self, event):
        """اختيار وجبة من القائمة"""
        selection = self.available_batches_tree.selection()
        if not selection:
            return
        
        item = self.available_batches_tree.item(selection[0])
        batch_number = item['values'][0]
        
        self.batch_number_var.set(str(batch_number))
        self.search_batch()
    
    def load_batch_samples(self):
        """تحميل عينات الوجبة"""
        if not self.current_batch_id:
            return
        
        conn = self.db.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.national_id, p.name, p.sample_type, t.name as test_name, 
                   bs.technician_name, r.result, r.notes, bs.id as batch_sample_id
            FROM batch_samples bs
            JOIN patients p ON bs.patient_id = p.id
            JOIN tests t ON bs.test_id = t.id
            LEFT JOIN results r ON r.patient_id = p.id AND r.test_id = t.id AND r.batch_id = bs.batch_id
            WHERE bs.batch_id = ?
            ORDER BY p.national_id
        ''', (self.current_batch_id,))
        
        results = cursor.fetchall()
        conn.close()
        
        # مسح النتائج السابقة
        for item in self.samples_tree.get_children():
            self.samples_tree.delete(item)
        
        # إضافة النتائج الجديدة
        for row in results:
            national_id, name, sample_type, test_name, technician, result, notes, batch_sample_id = row
            technician_display = technician or 'غير محدد'
            result_display = result or 'لم يتم إدخال النتيجة'
            notes_display = notes or ''
            
            item_id = self.samples_tree.insert('', tk.END, values=(
                national_id, name, sample_type, test_name, technician_display, result_display, notes_display
            ))
            
            # حفظ معرف العينة في الوجبة
            self.samples_tree.set(item_id, 'batch_sample_id', batch_sample_id)
    
    def on_sample_select(self, event):
        """عند تحديد عينة"""
        selection = self.samples_tree.selection()
        if not selection:
            return
        
        item = self.samples_tree.item(selection[0])
        values = item['values']
        
        # عرض النتيجة والملاحظات الحالية
        current_result = values[5]
        current_notes = values[6]
        
        if current_result != 'لم يتم إدخال النتيجة':
            self.result_var.set(current_result)
        else:
            self.result_var.set('')
        
        self.notes_var.set(current_notes)
    
    def set_quick_result(self, result):
        """تعيين نتيجة سريعة"""
        self.result_var.set(result)
        self.save_result()
    
    def save_result(self):
        """حفظ النتيجة للعينة المحددة"""
        selection = self.samples_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى تحديد عينة")
            return
        
        result = self.result_var.get()
        if not result:
            messagebox.showwarning("تحذير", "يرجى اختيار نتيجة")
            return
        
        notes = self.notes_var.get().strip()
        
        try:
            item = self.samples_tree.item(selection[0])
            values = item['values']
            national_id = values[0]
            test_name = values[3]
            
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # الحصول على معرفات المريض والتحليل
            cursor.execute('''
                SELECT p.id, t.id FROM patients p, tests t
                WHERE p.national_id = ? AND t.name = ?
            ''', (national_id, test_name))
            
            patient_test = cursor.fetchone()
            if not patient_test:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات المريض أو التحليل")
                conn.close()
                return
            
            patient_id, test_id = patient_test
            
            # التحقق من وجود نتيجة سابقة
            cursor.execute('''
                SELECT id FROM results 
                WHERE patient_id = ? AND test_id = ? AND batch_id = ?
            ''', (patient_id, test_id, self.current_batch_id))
            
            existing_result = cursor.fetchone()
            
            if existing_result:
                # تحديث النتيجة الموجودة
                cursor.execute('''
                    UPDATE results 
                    SET result = ?, notes = ?, result_date = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (result, notes, existing_result[0]))
            else:
                # إدراج نتيجة جديدة
                cursor.execute('''
                    INSERT INTO results (patient_id, test_id, batch_id, result, notes)
                    VALUES (?, ?, ?, ?, ?)
                ''', (patient_id, test_id, self.current_batch_id, result, notes))
            
            conn.commit()
            conn.close()
            
            # تحديث العرض
            self.samples_tree.item(selection[0], values=(
                values[0], values[1], values[2], values[3], values[4], result, notes
            ))
            
            messagebox.showinfo("نجح", "تم حفظ النتيجة بنجاح")
            
            # مسح الحقول
            self.result_var.set('')
            self.notes_var.set('')
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتيجة: {str(e)}")
    
    def save_all_results(self):
        """حفظ جميع النتائج المدخلة"""
        if not self.current_batch_id:
            messagebox.showwarning("تحذير", "لا توجد وجبة محددة")
            return
        
        # جمع جميع النتائج المدخلة
        results_to_save = []
        
        for item in self.samples_tree.get_children():
            values = self.samples_tree.item(item, 'values')
            if values[5] != 'لم يتم إدخال النتيجة':
                results_to_save.append({
                    'national_id': values[0],
                    'test_name': values[3],
                    'result': values[5],
                    'notes': values[6]
                })
        
        if not results_to_save:
            messagebox.showwarning("تحذير", "لا توجد نتائج لحفظها")
            return
        
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            saved_count = 0
            
            for result_data in results_to_save:
                # الحصول على معرفات المريض والتحليل
                cursor.execute('''
                    SELECT p.id, t.id FROM patients p, tests t
                    WHERE p.national_id = ? AND t.name = ?
                ''', (result_data['national_id'], result_data['test_name']))
                
                patient_test = cursor.fetchone()
                if not patient_test:
                    continue
                
                patient_id, test_id = patient_test
                
                # التحقق من وجود نتيجة سابقة
                cursor.execute('''
                    SELECT id FROM results 
                    WHERE patient_id = ? AND test_id = ? AND batch_id = ?
                ''', (patient_id, test_id, self.current_batch_id))
                
                existing_result = cursor.fetchone()
                
                if existing_result:
                    # تحديث النتيجة الموجودة
                    cursor.execute('''
                        UPDATE results 
                        SET result = ?, notes = ?, result_date = CURRENT_TIMESTAMP
                        WHERE id = ?
                    ''', (result_data['result'], result_data['notes'], existing_result[0]))
                else:
                    # إدراج نتيجة جديدة
                    cursor.execute('''
                        INSERT INTO results (patient_id, test_id, batch_id, result, notes)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (patient_id, test_id, self.current_batch_id, result_data['result'], result_data['notes']))
                
                saved_count += 1
            
            conn.commit()
            conn.close()
            
            messagebox.showinfo("نجح", f"تم حفظ {saved_count} نتيجة بنجاح")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ النتائج: {str(e)}")
    
    def clear_selection(self):
        """مسح التحديد والحقول"""
        self.result_var.set('')
        self.notes_var.set('')
        
        # إلغاء تحديد العينة
        for item in self.samples_tree.selection():
            self.samples_tree.selection_remove(item)
    
    def show(self):
        """عرض التبويب"""
        self.frame.pack(fill=tk.BOTH, expand=True)
        # تحديث البيانات عند العرض
        self.load_available_batches()
    
    def hide(self):
        """إخفاء التبويب"""
        self.frame.pack_forget()
    
    def refresh(self):
        """تحديث التبويب"""
        self.load_available_batches()
        if self.current_batch_id:
            self.load_batch_samples()
