#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import tkinter.font as tkFont

class SimpleLabApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مختبر الصحة العامة المركزي - ذي قار")
        self.root.geometry("800x600")
        
        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=10, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=14, weight="bold")
        
        # الألوان
        self.colors = {
            'primary': '#2E86AB',
            'secondary': '#A23B72', 
            'accent': '#F18F01',
            'success': '#C73E1D',
            'background': '#F5F5F5',
            'surface': '#FFFFFF',
            'text': '#2C3E50'
        }
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # عنوان البرنامج
        title_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=80)
        title_frame.pack(fill=tk.X, pady=(0, 10))
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, 
                              text="مختبر الصحة العامة المركزي - ذي قار",
                              font=self.header_font,
                              bg=self.colors['primary'],
                              fg='white')
        title_label.pack(expand=True)
        
        # إطار المحتوى
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # إطار التبويبات الجانبية
        sidebar_frame = tk.Frame(content_frame, 
                                bg=self.colors['surface'], 
                                width=200,
                                relief=tk.RAISED,
                                borderwidth=2)
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        sidebar_frame.pack_propagate(False)
        
        # إطار المحتوى الرئيسي
        main_content_frame = tk.Frame(content_frame, 
                                     bg=self.colors['surface'],
                                     relief=tk.RAISED,
                                     borderwidth=2)
        main_content_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # عنوان التبويبات
        sidebar_title = tk.Label(sidebar_frame,
                                text="القوائم",
                                font=self.font,
                                bg=self.colors['surface'],
                                fg=self.colors['text'])
        sidebar_title.pack(pady=10)
        
        # أزرار التبويبات
        tabs = [
            ('إدخال البيانات', self.colors['primary']),
            ('العمل', self.colors['secondary']),
            ('النتائج', self.colors['accent']),
            ('التقارير', self.colors['success']),
            ('الإعدادات', self.colors['text'])
        ]
        
        for tab_name, color in tabs:
            btn = tk.Button(sidebar_frame,
                           text=tab_name,
                           font=self.font,
                           bg=color,
                           fg='white',
                           activebackground=color,
                           activeforeground='white',
                           relief=tk.RAISED,
                           borderwidth=3,
                           width=18,
                           height=2,
                           command=lambda name=tab_name: self.show_tab(name))
            btn.pack(pady=5, padx=10, fill=tk.X)
        
        # محتوى التبويب
        self.content_label = tk.Label(main_content_frame,
                                     text="مرحباً بك في مختبر الصحة العامة المركزي - ذي قار\n\nاختر تبويباً من القائمة الجانبية للبدء",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'],
                                     justify=tk.CENTER)
        self.content_label.pack(expand=True)
        
        # زر اختبار قاعدة البيانات
        test_btn = tk.Button(main_content_frame,
                            text="اختبار قاعدة البيانات",
                            font=self.font,
                            bg=self.colors['primary'],
                            fg='white',
                            width=20,
                            height=2,
                            relief=tk.RAISED,
                            borderwidth=3,
                            command=self.test_database)
        test_btn.pack(pady=20)
    
    def show_tab(self, tab_name):
        """عرض محتوى التبويب"""
        self.content_label.config(text=f"تم اختيار تبويب: {tab_name}\n\nهذا اختبار للواجهة الأساسية")
        messagebox.showinfo("تبويب", f"تم النقر على تبويب: {tab_name}")
    
    def test_database(self):
        """اختبار قاعدة البيانات"""
        try:
            from database import DatabaseManager
            db = DatabaseManager()
            messagebox.showinfo("نجح", "تم إنشاء قاعدة البيانات بنجاح!")
            self.content_label.config(text="تم اختبار قاعدة البيانات بنجاح!\n\nيمكنك الآن استخدام البرنامج الكامل")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قاعدة البيانات: {str(e)}")
    
    def run(self):
        """تشغيل البرنامج"""
        self.root.mainloop()

if __name__ == "__main__":
    try:
        app = SimpleLabApp()
        app.run()
    except Exception as e:
        print(f"خطأ في تشغيل البرنامج: {str(e)}")
        import traceback
        traceback.print_exc()
