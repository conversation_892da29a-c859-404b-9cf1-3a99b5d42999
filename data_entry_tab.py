import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from datetime import datetime, date
import csv
from typing import List, Dict

class DataEntryTab:
    def __init__(self, parent, db, main_window):
        self.parent = parent
        self.db = db
        self.main_window = main_window
        self.frame = None
        self.current_patient_id = None
        
        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=10, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=12, weight="bold")
        
        # الألوان
        self.colors = main_window.colors
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة إدخال البيانات"""
        self.frame = tk.Frame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = tk.Label(self.frame,
                              text="إدخال بيانات المرضى والعينات",
                              font=self.header_font,
                              bg=self.colors['surface'],
                              fg=self.colors['text'])
        title_label.pack(pady=10)
        
        # إطار رئيسي للمحتوى
        main_content = tk.Frame(self.frame, bg=self.colors['surface'])
        main_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إطار النموذج
        form_frame = tk.LabelFrame(main_content,
                                  text="بيانات المريض والعينة",
                                  font=self.font,
                                  bg=self.colors['surface'],
                                  fg=self.colors['text'])
        form_frame.pack(fill=tk.X, pady=10)
        
        # إنشاء حقول النموذج
        self.create_form_fields(form_frame)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_content, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, pady=10)
        
        self.create_buttons(buttons_frame)
        
        # إطار البحث والعرض
        search_frame = tk.LabelFrame(main_content,
                                    text="البحث والعرض",
                                    font=self.font,
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'])
        search_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        self.create_search_section(search_frame)
    
    def create_form_fields(self, parent):
        """إنشاء حقول النموذج"""
        # إطار للحقول - عمودين
        fields_frame = tk.Frame(parent, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # العمود الأول
        left_column = tk.Frame(fields_frame, bg=self.colors['surface'])
        left_column.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # العمود الثاني
        right_column = tk.Frame(fields_frame, bg=self.colors['surface'])
        right_column.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # متغيرات النموذج
        self.form_vars = {}
        
        # الحقول الإلزامية - العمود الأول
        mandatory_fields_left = [
            ('name', 'الاسم *', 'entry'),
            ('age', 'العمر *', 'entry'),
            ('gender', 'الجنس *', 'combobox'),
            ('address', 'العنوان *', 'entry'),
            ('phone', 'رقم الهاتف *', 'entry')
        ]
        
        for field_id, label_text, field_type in mandatory_fields_left:
            self.create_field(left_column, field_id, label_text, field_type)
        
        # الحقول الإلزامية - العمود الثاني
        mandatory_fields_right = [
            ('sample_type', 'نوع العينة *', 'combobox'),
            ('sender_organization', 'جهة الإرسال *', 'combobox'),
            ('sample_collection_date', 'تاريخ سحب العينة *', 'date'),
            ('passport_number', 'رقم الجواز', 'entry'),
            ('receipt_number', 'رقم الوصل', 'entry')
        ]
        
        for field_id, label_text, field_type in mandatory_fields_right:
            self.create_field(right_column, field_id, label_text, field_type)
        
        # الحقول التلقائية
        auto_frame = tk.Frame(parent, bg=self.colors['surface'])
        auto_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الرقم الوطني (تلقائي)
        national_id_frame = tk.Frame(auto_frame, bg=self.colors['surface'])
        national_id_frame.pack(side=tk.LEFT, padx=10)
        
        tk.Label(national_id_frame, text="الرقم الوطني:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(anchor='w')
        
        self.form_vars['national_id'] = tk.StringVar()
        national_id_entry = tk.Entry(national_id_frame, textvariable=self.form_vars['national_id'],
                                    font=self.font, state='readonly', width=15)
        national_id_entry.pack(pady=2)
        
        # تاريخ استلام العينة (تلقائي)
        received_date_frame = tk.Frame(auto_frame, bg=self.colors['surface'])
        received_date_frame.pack(side=tk.LEFT, padx=10)
        
        tk.Label(received_date_frame, text="تاريخ استلام العينة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(anchor='w')
        
        self.form_vars['sample_received_date'] = tk.StringVar()
        received_date_entry = tk.Entry(received_date_frame, textvariable=self.form_vars['sample_received_date'],
                                      font=self.font, state='readonly', width=20)
        received_date_entry.pack(pady=2)
        
        # إطار التحاليل
        tests_frame = tk.LabelFrame(parent, text="التحاليل المطلوبة", font=self.font,
                                   bg=self.colors['surface'], fg=self.colors['text'])
        tests_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.create_tests_section(tests_frame)
        
        # تحديث القيم التلقائية
        self.update_auto_fields()
    
    def create_field(self, parent, field_id, label_text, field_type):
        """إنشاء حقل واحد"""
        field_frame = tk.Frame(parent, bg=self.colors['surface'])
        field_frame.pack(fill=tk.X, pady=5)
        
        # التسمية
        label = tk.Label(field_frame, text=label_text, font=self.font,
                        bg=self.colors['surface'], fg=self.colors['text'])
        label.pack(anchor='w')
        
        # الحقل
        self.form_vars[field_id] = tk.StringVar()
        
        if field_type == 'entry':
            widget = tk.Entry(field_frame, textvariable=self.form_vars[field_id],
                             font=self.font, width=25)
        elif field_type == 'combobox':
            widget = ttk.Combobox(field_frame, textvariable=self.form_vars[field_id],
                                 font=self.font, width=23, state='readonly')
            self.setup_combobox_values(field_id, widget)
        elif field_type == 'date':
            widget = tk.Entry(field_frame, textvariable=self.form_vars[field_id],
                             font=self.font, width=25)
            # تعيين التاريخ الحالي كقيمة افتراضية
            self.form_vars[field_id].set(date.today().strftime('%Y-%m-%d'))
        
        widget.pack(pady=2)
    
    def setup_combobox_values(self, field_id, combobox):
        """إعداد قيم القوائم المنسدلة"""
        if field_id == 'gender':
            combobox['values'] = ('M', 'F')
        elif field_id == 'sample_type':
            # جلب أنواع العينات من قاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT name FROM sample_types ORDER BY name')
            values = [row[0] for row in cursor.fetchall()]
            conn.close()
            combobox['values'] = values
        elif field_id == 'sender_organization':
            # جلب جهات الإرسال من قاعدة البيانات
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT name FROM sender_organizations ORDER BY name')
            values = [row[0] for row in cursor.fetchall()]
            conn.close()
            combobox['values'] = values
    
    def create_tests_section(self, parent):
        """إنشاء قسم التحاليل"""
        # إطار للتحاليل المتاحة
        available_frame = tk.Frame(parent, bg=self.colors['surface'])
        available_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5)

        tk.Label(available_frame, text="التحاليل المتاحة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(anchor='w')

        # إطار للقائمة مع شريط التمرير
        available_list_frame = tk.Frame(available_frame, bg=self.colors['surface'])
        available_list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # قائمة التحاليل المتاحة مع تكبير الحجم
        self.available_tests_listbox = tk.Listbox(available_list_frame,
                                                 font=tkFont.Font(family="Arial", size=11, weight="bold"),
                                                 height=8, selectmode=tk.MULTIPLE,
                                                 bg='white', fg=self.colors['text'],
                                                 selectbackground=self.colors['primary'],
                                                 selectforeground='white',
                                                 relief=tk.RAISED, borderwidth=2)

        # شريط التمرير للتحاليل المتاحة
        available_scrollbar = tk.Scrollbar(available_list_frame, orient=tk.VERTICAL,
                                          command=self.available_tests_listbox.yview)
        self.available_tests_listbox.configure(yscrollcommand=available_scrollbar.set)

        self.available_tests_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # إطار للأزرار مع تكبير الأزرار
        buttons_frame = tk.Frame(parent, bg=self.colors['surface'])
        buttons_frame.pack(side=tk.LEFT, padx=15)

        add_btn = tk.Button(buttons_frame, text="إضافة >>",
                           font=tkFont.Font(family="Arial", size=11, weight="bold"),
                           bg=self.colors['primary'], fg='white',
                           width=12, height=2,
                           relief=tk.RAISED, borderwidth=3,
                           command=self.add_selected_tests)
        add_btn.pack(pady=10)

        remove_btn = tk.Button(buttons_frame, text="<< إزالة",
                              font=tkFont.Font(family="Arial", size=11, weight="bold"),
                              bg=self.colors['success'], fg='white',
                              width=12, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.remove_selected_tests)
        remove_btn.pack(pady=10)

        # زر مسح الكل
        clear_all_btn = tk.Button(buttons_frame, text="مسح الكل",
                                 font=tkFont.Font(family="Arial", size=11, weight="bold"),
                                 bg=self.colors['accent'], fg='white',
                                 width=12, height=2,
                                 relief=tk.RAISED, borderwidth=3,
                                 command=self.clear_all_tests)
        clear_all_btn.pack(pady=10)

        # إطار للتحاليل المحددة
        selected_frame = tk.Frame(parent, bg=self.colors['surface'])
        selected_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)

        tk.Label(selected_frame, text="التحاليل المحددة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(anchor='w')

        # إطار للقائمة مع شريط التمرير
        selected_list_frame = tk.Frame(selected_frame, bg=self.colors['surface'])
        selected_list_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # قائمة التحاليل المحددة مع تكبير الحجم
        self.selected_tests_listbox = tk.Listbox(selected_list_frame,
                                                font=tkFont.Font(family="Arial", size=11, weight="bold"),
                                                height=8, selectmode=tk.MULTIPLE,
                                                bg='#f0f8ff', fg=self.colors['text'],
                                                selectbackground=self.colors['secondary'],
                                                selectforeground='white',
                                                relief=tk.RAISED, borderwidth=2)

        # شريط التمرير للتحاليل المحددة
        selected_scrollbar = tk.Scrollbar(selected_list_frame, orient=tk.VERTICAL,
                                         command=self.selected_tests_listbox.yview)
        self.selected_tests_listbox.configure(yscrollcommand=selected_scrollbar.set)

        self.selected_tests_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # تحميل التحاليل المتاحة
        self.load_available_tests()
    
    def load_available_tests(self):
        """تحميل التحاليل المتاحة"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name FROM tests ORDER BY name')
        self.available_tests = cursor.fetchall()
        conn.close()
        
        self.available_tests_listbox.delete(0, tk.END)
        for test_id, test_name in self.available_tests:
            self.available_tests_listbox.insert(tk.END, test_name)
    
    def add_selected_tests(self):
        """إضافة التحاليل المحددة"""
        selected_indices = self.available_tests_listbox.curselection()
        for index in selected_indices:
            test_name = self.available_tests_listbox.get(index)
            if test_name not in self.selected_tests_listbox.get(0, tk.END):
                self.selected_tests_listbox.insert(tk.END, test_name)
    
    def remove_selected_tests(self):
        """إزالة التحاليل المحددة"""
        selected_indices = list(self.selected_tests_listbox.curselection())
        selected_indices.reverse()  # البدء من الأسفل لتجنب تغيير الفهارس
        for index in selected_indices:
            self.selected_tests_listbox.delete(index)

    def clear_all_tests(self):
        """مسح جميع التحاليل المحددة"""
        self.selected_tests_listbox.delete(0, tk.END)

    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        # الأزرار الرئيسية
        main_buttons = [
            ('إضافة', self.colors['primary'], self.add_patient),
            ('تعديل', self.colors['secondary'], self.edit_patient),
            ('حذف', self.colors['success'], self.delete_patient),
            ('جديد', self.colors['accent'], self.clear_form),
            ('طباعة استيكر', self.colors['text'], self.print_sticker)
        ]

        buttons_frame1 = tk.Frame(parent, bg=self.colors['surface'])
        buttons_frame1.pack(pady=5)

        for text, color, command in main_buttons:
            btn = tk.Button(buttons_frame1, text=text, font=self.font,
                           bg=color, fg='white', width=12, height=2,
                           relief=tk.RAISED, borderwidth=3,
                           command=command)
            btn.pack(side=tk.LEFT, padx=5)

        # أزرار الاستيراد
        import_frame = tk.Frame(parent, bg=self.colors['surface'])
        import_frame.pack(pady=5)

        import_btn = tk.Button(import_frame, text="استيراد من Excel", font=self.font,
                              bg=self.colors['primary'], fg='white', width=15, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.import_from_excel)
        import_btn.pack(side=tk.LEFT, padx=5)

        export_btn = tk.Button(import_frame, text="تصدير إلى Excel", font=self.font,
                              bg=self.colors['secondary'], fg='white', width=15, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.export_to_excel)
        export_btn.pack(side=tk.LEFT, padx=5)

    def create_search_section(self, parent):
        """إنشاء قسم البحث والعرض"""
        # إطار البحث
        search_frame = tk.Frame(parent, bg=self.colors['surface'])
        search_frame.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(search_frame, text="البحث:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var,
                               font=self.font, width=30)
        search_entry.pack(side=tk.LEFT, padx=5)

        search_btn = tk.Button(search_frame, text="بحث", font=self.font,
                              bg=self.colors['primary'], fg='white',
                              command=self.search_patients)
        search_btn.pack(side=tk.LEFT, padx=5)

        # ربط البحث بالكتابة
        self.search_var.trace('w', lambda *args: self.search_patients())

        # جدول النتائج
        columns = ('الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'نوع العينة', 'جهة الإرسال', 'تاريخ السحب')
        self.patients_tree = ttk.Treeview(parent, columns=columns, show='headings', height=10)

        # إعداد العناوين
        for col in columns:
            self.patients_tree.heading(col, text=col)
            self.patients_tree.column(col, width=120, anchor='center')

        # شريط التمرير
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.patients_tree.yview)
        self.patients_tree.configure(yscrollcommand=scrollbar.set)

        # تعبئة الجدول
        self.patients_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # ربط النقر المزدوج
        self.patients_tree.bind('<Double-1>', self.on_patient_select)

        # تحميل البيانات
        self.load_patients()

    def update_auto_fields(self):
        """تحديث الحقول التلقائية"""
        # الرقم الوطني التالي
        next_id = self.db.get_next_national_id()
        self.form_vars['national_id'].set(str(next_id))

        # تاريخ استلام العينة الحالي
        current_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.form_vars['sample_received_date'].set(current_datetime)

    def validate_form(self):
        """التحقق من صحة البيانات"""
        required_fields = ['name', 'age', 'gender', 'address', 'phone',
                          'sample_type', 'sender_organization', 'sample_collection_date']

        for field in required_fields:
            if not self.form_vars[field].get().strip():
                messagebox.showerror("خطأ", f"يرجى ملء حقل {field}")
                return False

        # التحقق من العمر
        try:
            age = int(self.form_vars['age'].get())
            if age < 0 or age > 150:
                messagebox.showerror("خطأ", "العمر غير صحيح")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "العمر يجب أن يكون رقماً")
            return False

        # التحقق من وجود تحاليل محددة
        if self.selected_tests_listbox.size() == 0:
            messagebox.showerror("خطأ", "يرجى تحديد تحليل واحد على الأقل")
            return False

        return True

    def add_patient(self):
        """إضافة مريض جديد"""
        if not self.validate_form():
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # إدراج بيانات المريض
            patient_data = (
                int(self.form_vars['national_id'].get()),
                self.form_vars['name'].get().strip(),
                int(self.form_vars['age'].get()),
                self.form_vars['gender'].get(),
                self.form_vars['address'].get().strip(),
                self.form_vars['phone'].get().strip(),
                self.form_vars['passport_number'].get().strip() or None,
                self.form_vars['receipt_number'].get().strip() or None,
                self.form_vars['sample_type'].get(),
                self.form_vars['sender_organization'].get(),
                self.form_vars['sample_collection_date'].get()
            )

            cursor.execute('''
                INSERT INTO patients (national_id, name, age, gender, address, phone,
                                    passport_number, receipt_number, sample_type,
                                    sender_organization, sample_collection_date)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', patient_data)

            patient_id = cursor.lastrowid

            # إدراج التحاليل المحددة
            selected_tests = self.selected_tests_listbox.get(0, tk.END)
            for test_name in selected_tests:
                # الحصول على معرف التحليل
                cursor.execute('SELECT id FROM tests WHERE name = ?', (test_name,))
                test_id = cursor.fetchone()[0]

                cursor.execute('''
                    INSERT INTO patient_tests (patient_id, test_id) VALUES (?, ?)
                ''', (patient_id, test_id))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة المريض بنجاح")

            # طباعة الاستيكر تلقائياً
            self.print_sticker_for_patient(patient_id)

            # تحديث العرض
            self.load_patients()
            self.clear_form()

            # تحديث التبويبات الأخرى
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة المريض: {str(e)}")

    def edit_patient(self):
        """تعديل بيانات المريض"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للتعديل")
            return

        if not self.validate_form():
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # تحديث بيانات المريض
            patient_data = (
                self.form_vars['name'].get().strip(),
                int(self.form_vars['age'].get()),
                self.form_vars['gender'].get(),
                self.form_vars['address'].get().strip(),
                self.form_vars['phone'].get().strip(),
                self.form_vars['passport_number'].get().strip() or None,
                self.form_vars['receipt_number'].get().strip() or None,
                self.form_vars['sample_type'].get(),
                self.form_vars['sender_organization'].get(),
                self.form_vars['sample_collection_date'].get(),
                self.current_patient_id
            )

            cursor.execute('''
                UPDATE patients SET name=?, age=?, gender=?, address=?, phone=?,
                                  passport_number=?, receipt_number=?, sample_type=?,
                                  sender_organization=?, sample_collection_date=?,
                                  updated_at=CURRENT_TIMESTAMP
                WHERE id=?
            ''', patient_data)

            # حذف التحاليل القديمة وإدراج الجديدة
            cursor.execute('DELETE FROM patient_tests WHERE patient_id=?', (self.current_patient_id,))

            selected_tests = self.selected_tests_listbox.get(0, tk.END)
            for test_name in selected_tests:
                cursor.execute('SELECT id FROM tests WHERE name = ?', (test_name,))
                test_id = cursor.fetchone()[0]

                cursor.execute('''
                    INSERT INTO patient_tests (patient_id, test_id) VALUES (?, ?)
                ''', (self.current_patient_id, test_id))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تعديل بيانات المريض بنجاح")

            # تحديث العرض
            self.load_patients()
            self.clear_form()

            # تحديث التبويبات الأخرى
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل المريض: {str(e)}")

    def delete_patient(self):
        """حذف المريض"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المريض؟"):
            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()

                # حذف التحاليل المرتبطة
                cursor.execute('DELETE FROM patient_tests WHERE patient_id=?', (self.current_patient_id,))

                # حذف النتائج المرتبطة
                cursor.execute('DELETE FROM results WHERE patient_id=?', (self.current_patient_id,))

                # حذف من الوجبات
                cursor.execute('DELETE FROM batch_samples WHERE patient_id=?', (self.current_patient_id,))

                # حذف المريض
                cursor.execute('DELETE FROM patients WHERE id=?', (self.current_patient_id,))

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف المريض بنجاح")

                # تحديث العرض
                self.load_patients()
                self.clear_form()

                # تحديث التبويبات الأخرى
                self.main_window.refresh_all_tabs()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف المريض: {str(e)}")

    def clear_form(self):
        """مسح النموذج"""
        for var in self.form_vars.values():
            var.set('')

        self.selected_tests_listbox.delete(0, tk.END)
        self.current_patient_id = None
        self.update_auto_fields()

        # تعيين التاريخ الحالي
        self.form_vars['sample_collection_date'].set(date.today().strftime('%Y-%m-%d'))

    def print_sticker(self):
        """طباعة استيكر للمريض المحدد"""
        if not self.current_patient_id:
            messagebox.showwarning("تحذير", "يرجى اختيار مريض لطباعة الاستيكر")
            return

        self.print_sticker_for_patient(self.current_patient_id)

    def print_sticker_for_patient(self, patient_id):
        """طباعة استيكر لمريض محدد"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # جلب بيانات المريض
            cursor.execute('''
                SELECT name, sample_type, national_id FROM patients WHERE id=?
            ''', (patient_id,))

            patient_data = cursor.fetchone()
            if not patient_data:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات المريض")
                return

            name, sample_type, national_id = patient_data

            # هنا يمكن إضافة كود طباعة الاستيكر الفعلي
            # للآن سنعرض رسالة تأكيد
            sticker_text = f"اسم المريض: {name}\nنوع العينة: {sample_type}\nالرقم الوطني: {national_id}"
            messagebox.showinfo("طباعة الاستيكر", f"تم إرسال الاستيكر للطباعة:\n\n{sticker_text}")

            conn.close()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الاستيكر: {str(e)}")

    def import_from_excel(self):
        """استيراد البيانات من ملف CSV"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف CSV",
            filetypes=[("CSV files", "*.csv")]
        )

        if not file_path:
            return

        try:
            # قراءة ملف CSV
            with open(file_path, 'r', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)

                # التحقق من وجود الأعمدة المطلوبة
                required_columns = ['name', 'age', 'gender', 'address', 'phone', 'sample_type', 'sender_organization', 'sample_collection_date']
                missing_columns = [col for col in required_columns if col not in reader.fieldnames]

                if missing_columns:
                    messagebox.showerror("خطأ", f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
                    return

                conn = self.db.get_connection()
                cursor = conn.cursor()

                imported_count = 0

                for row_num, row in enumerate(reader, 1):
                    try:
                        # الحصول على الرقم الوطني التالي
                        national_id = self.db.get_next_national_id()

                        # إدراج المريض
                        patient_data = (
                            national_id,
                            str(row['name']).strip(),
                            int(row['age']),
                            str(row['gender']).strip(),
                            str(row['address']).strip(),
                            str(row['phone']).strip(),
                            str(row.get('passport_number', '')).strip() or None,
                            str(row.get('receipt_number', '')).strip() or None,
                            str(row['sample_type']).strip(),
                            str(row['sender_organization']).strip(),
                            str(row['sample_collection_date'])
                        )

                        cursor.execute('''
                            INSERT INTO patients (national_id, name, age, gender, address, phone,
                                                passport_number, receipt_number, sample_type,
                                                sender_organization, sample_collection_date)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', patient_data)

                        patient_id = cursor.lastrowid

                        # إضافة تحليل افتراضي إذا لم يتم تحديد تحاليل
                        if 'tests' in row and row['tests'].strip():
                            test_names = str(row['tests']).split(',')
                            for test_name in test_names:
                                test_name = test_name.strip()
                                cursor.execute('SELECT id FROM tests WHERE name = ?', (test_name,))
                                test_result = cursor.fetchone()
                                if test_result:
                                    cursor.execute('''
                                        INSERT INTO patient_tests (patient_id, test_id) VALUES (?, ?)
                                    ''', (patient_id, test_result[0]))
                        else:
                            # إضافة تحليل افتراضي
                            cursor.execute('SELECT id FROM tests LIMIT 1')
                            default_test = cursor.fetchone()
                            if default_test:
                                cursor.execute('''
                                    INSERT INTO patient_tests (patient_id, test_id) VALUES (?, ?)
                                ''', (patient_id, default_test[0]))

                        # طباعة استيكر للمريض
                        self.print_sticker_for_patient(patient_id)

                        imported_count += 1

                    except Exception as e:
                        print(f"خطأ في الصف {row_num}: {str(e)}")
                        continue

                conn.commit()
                conn.close()

                messagebox.showinfo("نجح الاستيراد", f"تم استيراد {imported_count} مريض بنجاح")

                # تحديث العرض
                self.load_patients()

                # تحديث التبويبات الأخرى
                self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استيراد البيانات: {str(e)}")

    def export_to_excel(self):
        """تصدير البيانات إلى ملف CSV"""
        file_path = filedialog.asksaveasfilename(
            title="حفظ ملف CSV",
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv")]
        )

        if not file_path:
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # جلب بيانات المرضى
            cursor.execute('''
                SELECT p.national_id, p.name, p.age, p.gender, p.address, p.phone,
                       p.passport_number, p.receipt_number, p.sample_type,
                       p.sender_organization, p.sample_collection_date,
                       p.sample_received_date
                FROM patients p
                ORDER BY p.national_id
            ''')

            results = cursor.fetchall()
            conn.close()

            # تصدير إلى CSV
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # كتابة العناوين
                headers = ['الرقم الوطني', 'الاسم', 'العمر', 'الجنس', 'العنوان', 'الهاتف',
                          'رقم الجواز', 'رقم الوصل', 'نوع العينة', 'جهة الإرسال',
                          'تاريخ سحب العينة', 'تاريخ استلام العينة']
                writer.writerow(headers)

                # كتابة البيانات
                for row in results:
                    writer.writerow(row)

            messagebox.showinfo("نجح التصدير", f"تم تصدير البيانات إلى {file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير البيانات: {str(e)}")

    def search_patients(self):
        """البحث في المرضى"""
        search_term = self.search_var.get().strip()

        conn = self.db.get_connection()
        cursor = conn.cursor()

        if search_term:
            query = '''
                SELECT p.id, p.national_id, p.name, p.age, p.gender, p.sample_type,
                       p.sender_organization, p.sample_collection_date
                FROM patients p
                WHERE p.name LIKE ? OR p.national_id LIKE ? OR p.phone LIKE ?
                ORDER BY p.national_id DESC
            '''
            cursor.execute(query, (f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'))
        else:
            query = '''
                SELECT p.id, p.national_id, p.name, p.age, p.gender, p.sample_type,
                       p.sender_organization, p.sample_collection_date
                FROM patients p
                ORDER BY p.national_id DESC
                LIMIT 100
            '''
            cursor.execute(query)

        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.patients_tree.get_children():
            self.patients_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            patient_id, national_id, name, age, gender, sample_type, sender_org, collection_date = row
            gender_text = 'ذكر' if gender == 'M' else 'أنثى'
            self.patients_tree.insert('', tk.END, values=(national_id, name, age, gender_text, sample_type, sender_org, collection_date))

    def load_patients(self):
        """تحميل جميع المرضى"""
        self.search_var.set('')
        self.search_patients()

    def on_patient_select(self, event):
        """عند اختيار مريض من الجدول"""
        selection = self.patients_tree.selection()
        if not selection:
            return

        item = self.patients_tree.item(selection[0])
        national_id = item['values'][0]

        # جلب بيانات المريض
        conn = self.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT * FROM patients WHERE national_id = ?
        ''', (national_id,))

        patient = cursor.fetchone()
        if not patient:
            conn.close()
            return

        # ملء النموذج
        self.current_patient_id = patient['id']
        self.form_vars['national_id'].set(str(patient['national_id']))
        self.form_vars['name'].set(patient['name'])
        self.form_vars['age'].set(str(patient['age']))
        self.form_vars['gender'].set(patient['gender'])
        self.form_vars['address'].set(patient['address'])
        self.form_vars['phone'].set(patient['phone'])
        self.form_vars['passport_number'].set(patient['passport_number'] or '')
        self.form_vars['receipt_number'].set(patient['receipt_number'] or '')
        self.form_vars['sample_type'].set(patient['sample_type'])
        self.form_vars['sender_organization'].set(patient['sender_organization'])
        self.form_vars['sample_collection_date'].set(patient['sample_collection_date'])
        self.form_vars['sample_received_date'].set(patient['sample_received_date'])

        # جلب التحاليل المرتبطة
        cursor.execute('''
            SELECT t.name FROM tests t
            JOIN patient_tests pt ON t.id = pt.test_id
            WHERE pt.patient_id = ?
        ''', (patient['id'],))

        patient_tests = cursor.fetchall()
        conn.close()

        # ملء قائمة التحاليل المحددة
        self.selected_tests_listbox.delete(0, tk.END)
        for test in patient_tests:
            self.selected_tests_listbox.insert(tk.END, test['name'])

    def show(self):
        """عرض التبويب"""
        self.frame.pack(fill=tk.BOTH, expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.frame.pack_forget()

    def refresh(self):
        """تحديث التبويب"""
        self.load_patients()
        self.load_available_tests()

        # تحديث قوائم الخيارات
        for widget in self.frame.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, ttk.Combobox):
                        # إعادة تحميل قيم القوائم المنسدلة
                        pass
