#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع وظائف برنامج مختبر الصحة العامة المركزي - ذي قار
"""

import sys
import os

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database():
    """اختبار قاعدة البيانات"""
    print("🔍 اختبار قاعدة البيانات...")
    try:
        from database import DatabaseManager
        db = DatabaseManager()
        
        # اختبار الاتصال
        conn = db.get_connection()
        cursor = conn.cursor()
        
        # اختبار الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"✓ تم العثور على {len(tables)} جدول في قاعدة البيانات")
        
        # اختبار البيانات الافتراضية
        cursor.execute("SELECT COUNT(*) FROM sample_types")
        sample_types_count = cursor.fetchone()[0]
        print(f"✓ أنواع العينات: {sample_types_count}")
        
        cursor.execute("SELECT COUNT(*) FROM tests")
        tests_count = cursor.fetchone()[0]
        print(f"✓ التحاليل: {tests_count}")
        
        cursor.execute("SELECT COUNT(*) FROM sender_organizations")
        senders_count = cursor.fetchone()[0]
        print(f"✓ جهات الإرسال: {senders_count}")
        
        cursor.execute("SELECT COUNT(*) FROM technicians")
        technicians_count = cursor.fetchone()[0]
        print(f"✓ الفنيين: {technicians_count}")
        
        conn.close()
        print("✅ قاعدة البيانات تعمل بشكل صحيح\n")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}\n")
        return False

def test_imports():
    """اختبار استيراد جميع الملفات"""
    print("🔍 اختبار استيراد الملفات...")
    
    modules = [
        ('database', 'قاعدة البيانات'),
        ('main_window', 'النافذة الرئيسية'),
        ('data_entry_tab', 'تبويب إدخال البيانات'),
        ('work_tab', 'تبويب العمل'),
        ('results_tab', 'تبويب النتائج'),
        ('reports_tab', 'تبويب التقارير'),
        ('settings_tab', 'تبويب الإعدادات')
    ]
    
    success_count = 0
    
    for module_name, description in modules:
        try:
            __import__(module_name)
            print(f"✓ {description}")
            success_count += 1
        except Exception as e:
            print(f"❌ {description}: {str(e)}")
    
    if success_count == len(modules):
        print("✅ جميع الملفات تم استيرادها بنجاح\n")
        return True
    else:
        print(f"⚠️ تم استيراد {success_count} من {len(modules)} ملف\n")
        return False

def test_tkinter():
    """اختبار مكتبة tkinter"""
    print("🔍 اختبار مكتبة tkinter...")
    try:
        import tkinter as tk
        from tkinter import ttk, messagebox, filedialog
        import tkinter.font as tkFont
        
        # إنشاء نافذة اختبار
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة
        
        # اختبار الخطوط
        font = tkFont.Font(family="Arial", size=10, weight="bold")
        
        # اختبار العناصر
        frame = tk.Frame(root)
        label = tk.Label(frame, text="اختبار", font=font)
        button = tk.Button(frame, text="زر اختبار")
        entry = tk.Entry(frame)
        combo = ttk.Combobox(frame)
        tree = ttk.Treeview(frame)
        
        root.destroy()
        print("✅ مكتبة tkinter تعمل بشكل صحيح\n")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في مكتبة tkinter: {str(e)}\n")
        return False

def test_file_operations():
    """اختبار عمليات الملفات"""
    print("🔍 اختبار عمليات الملفات...")
    try:
        import csv
        from datetime import datetime, date
        
        # اختبار كتابة CSV
        test_file = "test_data.csv"
        with open(test_file, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['اسم', 'عمر', 'تاريخ'])
            writer.writerow(['اختبار', '25', datetime.now().strftime('%Y-%m-%d')])
        
        # اختبار قراءة CSV
        with open(test_file, 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            rows = list(reader)
        
        # حذف ملف الاختبار
        os.remove(test_file)
        
        print("✅ عمليات الملفات تعمل بشكل صحيح\n")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في عمليات الملفات: {str(e)}\n")
        return False

def test_main_window():
    """اختبار النافذة الرئيسية"""
    print("🔍 اختبار النافذة الرئيسية...")
    try:
        from main_window import MainWindow
        
        # إنشاء النافذة (بدون تشغيلها)
        app = MainWindow()
        
        # اختبار وجود العناصر الأساسية
        if hasattr(app, 'root') and hasattr(app, 'db') and hasattr(app, 'tab_frames'):
            print("✅ النافذة الرئيسية تم إنشاؤها بنجاح\n")
            return True
        else:
            print("❌ النافذة الرئيسية لا تحتوي على العناصر المطلوبة\n")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في النافذة الرئيسية: {str(e)}\n")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("=" * 60)
    print("🧪 اختبار برنامج مختبر الصحة العامة المركزي - ذي قار")
    print("=" * 60)
    print()
    
    tests = [
        test_tkinter,
        test_imports,
        test_database,
        test_file_operations,
        test_main_window
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test in tests:
        if test():
            passed_tests += 1
    
    print("=" * 60)
    print(f"📊 نتائج الاختبار: {passed_tests}/{total_tests} اختبار نجح")
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت! البرنامج جاهز للاستخدام")
        print("🚀 يمكنك الآن تشغيل البرنامج باستخدام: py run_lab_fixed.py")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
