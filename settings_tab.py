import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import tkinter.font as tkFont
from typing import List, Dict

class SettingsTab:
    def __init__(self, parent, db, main_window):
        self.parent = parent
        self.db = db
        self.main_window = main_window
        self.frame = None
        
        # إعداد الخطوط
        self.font = tkFont.Font(family="Arial", size=12, weight="bold")
        self.header_font = tkFont.Font(family="Arial", size=14, weight="bold")
        
        # الألوان
        self.colors = main_window.colors
        
        self.create_widgets()
    
    def create_widgets(self):
        """إنشاء عناصر واجهة الإعدادات"""
        self.frame = tk.Frame(self.parent, bg=self.colors['surface'])
        
        # عنوان التبويب
        title_label = tk.Label(self.frame,
                              text="إعدادات النظام",
                              font=self.header_font,
                              bg=self.colors['surface'],
                              fg=self.colors['text'])
        title_label.pack(pady=10)
        
        # إطار رئيسي للمحتوى
        main_content = tk.Frame(self.frame, bg=self.colors['surface'])
        main_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # إنشاء التبويبات الفرعية
        notebook = ttk.Notebook(main_content)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب أنواع العينات
        self.sample_types_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(self.sample_types_frame, text="أنواع العينات")
        self.create_sample_types_section()
        
        # تبويب جهات الإرسال
        self.senders_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(self.senders_frame, text="جهات الإرسال")
        self.create_senders_section()
        
        # تبويب التحاليل
        self.tests_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(self.tests_frame, text="التحاليل")
        self.create_tests_section()
        
        # تبويب الفنيين
        self.technicians_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(self.technicians_frame, text="الفنيين")
        self.create_technicians_section()
        
        # تبويب إعدادات التقرير
        self.report_settings_frame = tk.Frame(notebook, bg=self.colors['surface'])
        notebook.add(self.report_settings_frame, text="إعدادات التقرير")
        self.create_report_settings_section()
    
    def create_sample_types_section(self):
        """إنشاء قسم أنواع العينات"""
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.sample_types_frame,
                                   text="إضافة/تعديل نوع العينة",
                                   font=self.font,
                                   bg=self.colors['surface'],
                                   fg=self.colors['text'])
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # حقول الإدخال
        fields_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(fields_frame, text="اسم نوع العينة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.sample_type_name_var = tk.StringVar()
        sample_type_entry = tk.Entry(fields_frame, textvariable=self.sample_type_name_var,
                                    font=self.font, width=20)
        sample_type_entry.pack(side=tk.LEFT, padx=5)
        
        tk.Label(fields_frame, text="الوصف:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))
        
        self.sample_type_desc_var = tk.StringVar()
        desc_entry = tk.Entry(fields_frame, textvariable=self.sample_type_desc_var,
                             font=self.font, width=30)
        desc_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        add_btn = tk.Button(buttons_frame, text="إضافة", font=self.font,
                           bg=self.colors['primary'], fg='white', width=10, height=2,
                           relief=tk.RAISED, borderwidth=3,
                           command=self.add_sample_type)
        add_btn.pack(side=tk.LEFT, padx=5)
        
        edit_btn = tk.Button(buttons_frame, text="تعديل", font=self.font,
                            bg=self.colors['secondary'], fg='white', width=10, height=2,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.edit_sample_type)
        edit_btn.pack(side=tk.LEFT, padx=5)
        
        delete_btn = tk.Button(buttons_frame, text="حذف", font=self.font,
                              bg=self.colors['success'], fg='white', width=10, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.delete_sample_type)
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(buttons_frame, text="مسح", font=self.font,
                             bg=self.colors['accent'], fg='white', width=10, height=2,
                             relief=tk.RAISED, borderwidth=3,
                             command=self.clear_sample_type_fields)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # قائمة أنواع العينات
        list_frame = tk.LabelFrame(self.sample_types_frame,
                                  text="أنواع العينات الموجودة",
                                  font=self.font,
                                  bg=self.colors['surface'],
                                  fg=self.colors['text'])
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # جدول أنواع العينات
        columns = ('المعرف', 'الاسم', 'الوصف')
        self.sample_types_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.sample_types_tree.heading(col, text=col)
            self.sample_types_tree.column(col, width=150, anchor='center')
        
        # شريط التمرير
        sample_types_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.sample_types_tree.yview)
        self.sample_types_tree.configure(yscrollcommand=sample_types_scrollbar.set)
        
        self.sample_types_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        sample_types_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # ربط النقر المزدوج
        self.sample_types_tree.bind('<Double-1>', self.on_sample_type_select)
        
        # تحميل البيانات
        self.load_sample_types()
    
    def create_senders_section(self):
        """إنشاء قسم جهات الإرسال"""
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.senders_frame,
                                   text="إضافة/تعديل جهة الإرسال",
                                   font=self.font,
                                   bg=self.colors['surface'],
                                   fg=self.colors['text'])
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # حقول الإدخال
        fields_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # الصف الأول
        row1 = tk.Frame(fields_frame, bg=self.colors['surface'])
        row1.pack(fill=tk.X, pady=5)
        
        tk.Label(row1, text="اسم الجهة:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.sender_name_var = tk.StringVar()
        sender_name_entry = tk.Entry(row1, textvariable=self.sender_name_var,
                                    font=self.font, width=30)
        sender_name_entry.pack(side=tk.LEFT, padx=5)
        
        # الصف الثاني
        row2 = tk.Frame(fields_frame, bg=self.colors['surface'])
        row2.pack(fill=tk.X, pady=5)
        
        tk.Label(row2, text="العنوان:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.sender_address_var = tk.StringVar()
        address_entry = tk.Entry(row2, textvariable=self.sender_address_var,
                                font=self.font, width=30)
        address_entry.pack(side=tk.LEFT, padx=5)
        
        tk.Label(row2, text="الهاتف:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))
        
        self.sender_phone_var = tk.StringVar()
        phone_entry = tk.Entry(row2, textvariable=self.sender_phone_var,
                              font=self.font, width=15)
        phone_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        add_btn = tk.Button(buttons_frame, text="إضافة", font=self.font,
                           bg=self.colors['primary'], fg='white', width=10, height=2,
                           relief=tk.RAISED, borderwidth=3,
                           command=self.add_sender)
        add_btn.pack(side=tk.LEFT, padx=5)
        
        edit_btn = tk.Button(buttons_frame, text="تعديل", font=self.font,
                            bg=self.colors['secondary'], fg='white', width=10, height=2,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.edit_sender)
        edit_btn.pack(side=tk.LEFT, padx=5)
        
        delete_btn = tk.Button(buttons_frame, text="حذف", font=self.font,
                              bg=self.colors['success'], fg='white', width=10, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.delete_sender)
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(buttons_frame, text="مسح", font=self.font,
                             bg=self.colors['accent'], fg='white', width=10, height=2,
                             relief=tk.RAISED, borderwidth=3,
                             command=self.clear_sender_fields)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # قائمة جهات الإرسال
        list_frame = tk.LabelFrame(self.senders_frame,
                                  text="جهات الإرسال الموجودة",
                                  font=self.font,
                                  bg=self.colors['surface'],
                                  fg=self.colors['text'])
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # جدول جهات الإرسال
        columns = ('المعرف', 'الاسم', 'العنوان', 'الهاتف')
        self.senders_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.senders_tree.heading(col, text=col)
            if col == 'المعرف':
                self.senders_tree.column(col, width=80, anchor='center')
            else:
                self.senders_tree.column(col, width=200, anchor='center')
        
        # شريط التمرير
        senders_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.senders_tree.yview)
        self.senders_tree.configure(yscrollcommand=senders_scrollbar.set)
        
        self.senders_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        senders_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # ربط النقر المزدوج
        self.senders_tree.bind('<Double-1>', self.on_sender_select)
        
        # تحميل البيانات
        self.load_senders()
    
    def create_tests_section(self):
        """إنشاء قسم التحاليل"""
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.tests_frame,
                                   text="إضافة/تعديل التحليل",
                                   font=self.font,
                                   bg=self.colors['surface'],
                                   fg=self.colors['text'])
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # حقول الإدخال
        fields_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)
        
        tk.Label(fields_frame, text="اسم التحليل:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)
        
        self.test_name_var = tk.StringVar()
        test_name_entry = tk.Entry(fields_frame, textvariable=self.test_name_var,
                                  font=self.font, width=25)
        test_name_entry.pack(side=tk.LEFT, padx=5)
        
        tk.Label(fields_frame, text="الوصف:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))
        
        self.test_desc_var = tk.StringVar()
        test_desc_entry = tk.Entry(fields_frame, textvariable=self.test_desc_var,
                                  font=self.font, width=30)
        test_desc_entry.pack(side=tk.LEFT, padx=5)
        
        # أزرار العمليات
        buttons_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        add_btn = tk.Button(buttons_frame, text="إضافة", font=self.font,
                           bg=self.colors['primary'], fg='white', width=10, height=2,
                           relief=tk.RAISED, borderwidth=3,
                           command=self.add_test)
        add_btn.pack(side=tk.LEFT, padx=5)
        
        edit_btn = tk.Button(buttons_frame, text="تعديل", font=self.font,
                            bg=self.colors['secondary'], fg='white', width=10, height=2,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.edit_test)
        edit_btn.pack(side=tk.LEFT, padx=5)
        
        delete_btn = tk.Button(buttons_frame, text="حذف", font=self.font,
                              bg=self.colors['success'], fg='white', width=10, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.delete_test)
        delete_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(buttons_frame, text="مسح", font=self.font,
                             bg=self.colors['accent'], fg='white', width=10, height=2,
                             relief=tk.RAISED, borderwidth=3,
                             command=self.clear_test_fields)
        clear_btn.pack(side=tk.LEFT, padx=5)
        
        # قائمة التحاليل
        list_frame = tk.LabelFrame(self.tests_frame,
                                  text="التحاليل الموجودة",
                                  font=self.font,
                                  bg=self.colors['surface'],
                                  fg=self.colors['text'])
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # جدول التحاليل
        columns = ('المعرف', 'الاسم', 'الوصف')
        self.tests_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.tests_tree.heading(col, text=col)
            if col == 'المعرف':
                self.tests_tree.column(col, width=80, anchor='center')
            else:
                self.tests_tree.column(col, width=250, anchor='center')
        
        # شريط التمرير
        tests_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.tests_tree.yview)
        self.tests_tree.configure(yscrollcommand=tests_scrollbar.set)
        
        self.tests_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        tests_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)
        
        # ربط النقر المزدوج
        self.tests_tree.bind('<Double-1>', self.on_test_select)
        
        # تحميل البيانات
        self.load_tests()

    def create_technicians_section(self):
        """إنشاء قسم الفنيين"""
        # إطار الإدخال
        input_frame = tk.LabelFrame(self.technicians_frame,
                                   text="إضافة/تعديل الفني",
                                   font=self.font,
                                   bg=self.colors['surface'],
                                   fg=self.colors['text'])
        input_frame.pack(fill=tk.X, padx=10, pady=10)

        # حقول الإدخال
        fields_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        fields_frame.pack(fill=tk.X, padx=10, pady=10)

        # الصف الأول
        row1 = tk.Frame(fields_frame, bg=self.colors['surface'])
        row1.pack(fill=tk.X, pady=5)

        tk.Label(row1, text="اسم الفني:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.tech_name_var = tk.StringVar()
        tech_name_entry = tk.Entry(row1, textvariable=self.tech_name_var,
                                  font=self.font, width=25)
        tech_name_entry.pack(side=tk.LEFT, padx=5)

        tk.Label(row1, text="التخصص:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT, padx=(20, 0))

        self.tech_spec_var = tk.StringVar()
        spec_entry = tk.Entry(row1, textvariable=self.tech_spec_var,
                             font=self.font, width=20)
        spec_entry.pack(side=tk.LEFT, padx=5)

        # الصف الثاني
        row2 = tk.Frame(fields_frame, bg=self.colors['surface'])
        row2.pack(fill=tk.X, pady=5)

        tk.Label(row2, text="الهاتف:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.tech_phone_var = tk.StringVar()
        phone_entry = tk.Entry(row2, textvariable=self.tech_phone_var,
                              font=self.font, width=15)
        phone_entry.pack(side=tk.LEFT, padx=5)

        # أزرار العمليات
        buttons_frame = tk.Frame(input_frame, bg=self.colors['surface'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        add_btn = tk.Button(buttons_frame, text="إضافة", font=self.font,
                           bg=self.colors['primary'], fg='white', width=10, height=2,
                           relief=tk.RAISED, borderwidth=3,
                           command=self.add_technician)
        add_btn.pack(side=tk.LEFT, padx=5)

        edit_btn = tk.Button(buttons_frame, text="تعديل", font=self.font,
                            bg=self.colors['secondary'], fg='white', width=10, height=2,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.edit_technician)
        edit_btn.pack(side=tk.LEFT, padx=5)

        delete_btn = tk.Button(buttons_frame, text="حذف", font=self.font,
                              bg=self.colors['success'], fg='white', width=10, height=2,
                              relief=tk.RAISED, borderwidth=3,
                              command=self.delete_technician)
        delete_btn.pack(side=tk.LEFT, padx=5)

        clear_btn = tk.Button(buttons_frame, text="مسح", font=self.font,
                             bg=self.colors['accent'], fg='white', width=10, height=2,
                             relief=tk.RAISED, borderwidth=3,
                             command=self.clear_technician_fields)
        clear_btn.pack(side=tk.LEFT, padx=5)

        # قائمة الفنيين
        list_frame = tk.LabelFrame(self.technicians_frame,
                                  text="الفنيين الموجودين",
                                  font=self.font,
                                  bg=self.colors['surface'],
                                  fg=self.colors['text'])
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # جدول الفنيين
        columns = ('المعرف', 'الاسم', 'التخصص', 'الهاتف')
        self.technicians_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=10)

        for col in columns:
            self.technicians_tree.heading(col, text=col)
            if col == 'المعرف':
                self.technicians_tree.column(col, width=80, anchor='center')
            else:
                self.technicians_tree.column(col, width=150, anchor='center')

        # شريط التمرير
        technicians_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.technicians_tree.yview)
        self.technicians_tree.configure(yscrollcommand=technicians_scrollbar.set)

        self.technicians_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=10, pady=10)
        technicians_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10)

        # ربط النقر المزدوج
        self.technicians_tree.bind('<Double-1>', self.on_technician_select)

        # تحميل البيانات
        self.load_technicians()

    def create_report_settings_section(self):
        """إنشاء قسم إعدادات التقرير"""
        # إطار الإعدادات
        settings_frame = tk.LabelFrame(self.report_settings_frame,
                                      text="إعدادات التقرير",
                                      font=self.font,
                                      bg=self.colors['surface'],
                                      fg=self.colors['text'])
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # الهيدر العربي
        arabic_frame = tk.LabelFrame(settings_frame,
                                    text="الهيدر العربي",
                                    font=self.font,
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'])
        arabic_frame.pack(fill=tk.X, padx=10, pady=10)

        self.arabic_header_var = tk.StringVar()
        arabic_text = tk.Text(arabic_frame, font=self.font, height=5, width=60)
        arabic_text.pack(padx=10, pady=10)

        # الهيدر الإنجليزي
        english_frame = tk.LabelFrame(settings_frame,
                                     text="الهيدر الإنجليزي",
                                     font=self.font,
                                     bg=self.colors['surface'],
                                     fg=self.colors['text'])
        english_frame.pack(fill=tk.X, padx=10, pady=10)

        self.english_header_var = tk.StringVar()
        english_text = tk.Text(english_frame, font=self.font, height=5, width=60)
        english_text.pack(padx=10, pady=10)

        # الفوتر
        footer_frame = tk.LabelFrame(settings_frame,
                                    text="معلومات الفوتر",
                                    font=self.font,
                                    bg=self.colors['surface'],
                                    fg=self.colors['text'])
        footer_frame.pack(fill=tk.X, padx=10, pady=10)

        # العنوان
        address_row = tk.Frame(footer_frame, bg=self.colors['surface'])
        address_row.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(address_row, text="العنوان:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.footer_address_var = tk.StringVar()
        address_entry = tk.Entry(address_row, textvariable=self.footer_address_var,
                                font=self.font, width=50)
        address_entry.pack(side=tk.LEFT, padx=5)

        # الإيميل
        email_row = tk.Frame(footer_frame, bg=self.colors['surface'])
        email_row.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(email_row, text="الإيميل:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.footer_email_var = tk.StringVar()
        email_entry = tk.Entry(email_row, textvariable=self.footer_email_var,
                              font=self.font, width=30)
        email_entry.pack(side=tk.LEFT, padx=5)

        # الشعار
        logo_row = tk.Frame(footer_frame, bg=self.colors['surface'])
        logo_row.pack(fill=tk.X, padx=10, pady=5)

        tk.Label(logo_row, text="مسار الشعار:", font=self.font,
                bg=self.colors['surface'], fg=self.colors['text']).pack(side=tk.LEFT)

        self.logo_path_var = tk.StringVar()
        logo_entry = tk.Entry(logo_row, textvariable=self.logo_path_var,
                             font=self.font, width=40)
        logo_entry.pack(side=tk.LEFT, padx=5)

        browse_btn = tk.Button(logo_row, text="تصفح", font=self.font,
                              bg=self.colors['primary'], fg='white',
                              command=self.browse_logo)
        browse_btn.pack(side=tk.LEFT, padx=5)

        # أزرار الحفظ
        save_frame = tk.Frame(settings_frame, bg=self.colors['surface'])
        save_frame.pack(fill=tk.X, padx=10, pady=20)

        save_btn = tk.Button(save_frame, text="حفظ الإعدادات", font=self.font,
                            bg=self.colors['primary'], fg='white', width=15, height=2,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.save_report_settings)
        save_btn.pack(side=tk.LEFT, padx=5)

        load_btn = tk.Button(save_frame, text="تحميل الإعدادات", font=self.font,
                            bg=self.colors['secondary'], fg='white', width=15, height=2,
                            relief=tk.RAISED, borderwidth=3,
                            command=self.load_report_settings)
        load_btn.pack(side=tk.LEFT, padx=5)

        # حفظ مراجع النصوص
        self.arabic_text_widget = arabic_text
        self.english_text_widget = english_text

        # تحميل الإعدادات الحالية
        self.load_report_settings()

    # وظائف أنواع العينات
    def load_sample_types(self):
        """تحميل أنواع العينات"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, description FROM sample_types ORDER BY name')
        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.sample_types_tree.get_children():
            self.sample_types_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            self.sample_types_tree.insert('', tk.END, values=row)

    def add_sample_type(self):
        """إضافة نوع عينة جديد"""
        name = self.sample_type_name_var.get().strip()
        description = self.sample_type_desc_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم نوع العينة")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('INSERT INTO sample_types (name, description) VALUES (?, ?)', (name, description))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة نوع العينة بنجاح")
            self.load_sample_types()
            self.clear_sample_type_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة نوع العينة: {str(e)}")

    def edit_sample_type(self):
        """تعديل نوع العينة المحدد"""
        selection = self.sample_types_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للتعديل")
            return

        item = self.sample_types_tree.item(selection[0])
        sample_type_id = item['values'][0]

        name = self.sample_type_name_var.get().strip()
        description = self.sample_type_desc_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم نوع العينة")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('UPDATE sample_types SET name=?, description=? WHERE id=?',
                          (name, description, sample_type_id))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تعديل نوع العينة بنجاح")
            self.load_sample_types()
            self.clear_sample_type_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل نوع العينة: {str(e)}")

    def delete_sample_type(self):
        """حذف نوع العينة المحدد"""
        selection = self.sample_types_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار نوع عينة للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف نوع العينة؟"):
            item = self.sample_types_tree.item(selection[0])
            sample_type_id = item['values'][0]

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                cursor.execute('DELETE FROM sample_types WHERE id=?', (sample_type_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف نوع العينة بنجاح")
                self.load_sample_types()
                self.clear_sample_type_fields()
                self.main_window.refresh_all_tabs()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف نوع العينة: {str(e)}")

    def clear_sample_type_fields(self):
        """مسح حقول نوع العينة"""
        self.sample_type_name_var.set('')
        self.sample_type_desc_var.set('')

    def on_sample_type_select(self, event):
        """عند اختيار نوع عينة"""
        selection = self.sample_types_tree.selection()
        if not selection:
            return

        item = self.sample_types_tree.item(selection[0])
        values = item['values']

        self.sample_type_name_var.set(values[1])
        self.sample_type_desc_var.set(values[2] or '')

    # وظائف جهات الإرسال
    def load_senders(self):
        """تحميل جهات الإرسال"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, address, phone FROM sender_organizations ORDER BY name')
        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.senders_tree.get_children():
            self.senders_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            self.senders_tree.insert('', tk.END, values=row)

    def add_sender(self):
        """إضافة جهة إرسال جديدة"""
        name = self.sender_name_var.get().strip()
        address = self.sender_address_var.get().strip()
        phone = self.sender_phone_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الجهة")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('INSERT INTO sender_organizations (name, address, phone) VALUES (?, ?, ?)',
                          (name, address, phone))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة جهة الإرسال بنجاح")
            self.load_senders()
            self.clear_sender_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة جهة الإرسال: {str(e)}")

    def edit_sender(self):
        """تعديل جهة الإرسال المحددة"""
        selection = self.senders_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للتعديل")
            return

        item = self.senders_tree.item(selection[0])
        sender_id = item['values'][0]

        name = self.sender_name_var.get().strip()
        address = self.sender_address_var.get().strip()
        phone = self.sender_phone_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الجهة")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('UPDATE sender_organizations SET name=?, address=?, phone=? WHERE id=?',
                          (name, address, phone, sender_id))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تعديل جهة الإرسال بنجاح")
            self.load_senders()
            self.clear_sender_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل جهة الإرسال: {str(e)}")

    def delete_sender(self):
        """حذف جهة الإرسال المحددة"""
        selection = self.senders_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار جهة إرسال للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف جهة الإرسال؟"):
            item = self.senders_tree.item(selection[0])
            sender_id = item['values'][0]

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                cursor.execute('DELETE FROM sender_organizations WHERE id=?', (sender_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف جهة الإرسال بنجاح")
                self.load_senders()
                self.clear_sender_fields()
                self.main_window.refresh_all_tabs()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف جهة الإرسال: {str(e)}")

    def clear_sender_fields(self):
        """مسح حقول جهة الإرسال"""
        self.sender_name_var.set('')
        self.sender_address_var.set('')
        self.sender_phone_var.set('')

    def on_sender_select(self, event):
        """عند اختيار جهة إرسال"""
        selection = self.senders_tree.selection()
        if not selection:
            return

        item = self.senders_tree.item(selection[0])
        values = item['values']

        self.sender_name_var.set(values[1])
        self.sender_address_var.set(values[2] or '')
        self.sender_phone_var.set(values[3] or '')

    # وظائف التحاليل
    def load_tests(self):
        """تحميل التحاليل"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, description FROM tests ORDER BY name')
        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.tests_tree.get_children():
            self.tests_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            self.tests_tree.insert('', tk.END, values=row)

    def add_test(self):
        """إضافة تحليل جديد"""
        name = self.test_name_var.get().strip()
        description = self.test_desc_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم التحليل")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('INSERT INTO tests (name, description) VALUES (?, ?)', (name, description))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة التحليل بنجاح")
            self.load_tests()
            self.clear_test_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة التحليل: {str(e)}")

    def edit_test(self):
        """تعديل التحليل المحدد"""
        selection = self.tests_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للتعديل")
            return

        item = self.tests_tree.item(selection[0])
        test_id = item['values'][0]

        name = self.test_name_var.get().strip()
        description = self.test_desc_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم التحليل")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('UPDATE tests SET name=?, description=? WHERE id=?',
                          (name, description, test_id))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تعديل التحليل بنجاح")
            self.load_tests()
            self.clear_test_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل التحليل: {str(e)}")

    def delete_test(self):
        """حذف التحليل المحدد"""
        selection = self.tests_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار تحليل للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف التحليل؟"):
            item = self.tests_tree.item(selection[0])
            test_id = item['values'][0]

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                cursor.execute('DELETE FROM tests WHERE id=?', (test_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف التحليل بنجاح")
                self.load_tests()
                self.clear_test_fields()
                self.main_window.refresh_all_tabs()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف التحليل: {str(e)}")

    def clear_test_fields(self):
        """مسح حقول التحليل"""
        self.test_name_var.set('')
        self.test_desc_var.set('')

    def on_test_select(self, event):
        """عند اختيار تحليل"""
        selection = self.tests_tree.selection()
        if not selection:
            return

        item = self.tests_tree.item(selection[0])
        values = item['values']

        self.test_name_var.set(values[1])
        self.test_desc_var.set(values[2] or '')

    # وظائف الفنيين
    def load_technicians(self):
        """تحميل الفنيين"""
        conn = self.db.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT id, name, specialization, phone FROM technicians ORDER BY name')
        results = cursor.fetchall()
        conn.close()

        # مسح النتائج السابقة
        for item in self.technicians_tree.get_children():
            self.technicians_tree.delete(item)

        # إضافة النتائج الجديدة
        for row in results:
            self.technicians_tree.insert('', tk.END, values=row)

    def add_technician(self):
        """إضافة فني جديد"""
        name = self.tech_name_var.get().strip()
        specialization = self.tech_spec_var.get().strip()
        phone = self.tech_phone_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الفني")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('INSERT INTO technicians (name, specialization, phone) VALUES (?, ?, ?)',
                          (name, specialization, phone))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم إضافة الفني بنجاح")
            self.load_technicians()
            self.clear_technician_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إضافة الفني: {str(e)}")

    def edit_technician(self):
        """تعديل الفني المحدد"""
        selection = self.technicians_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فني للتعديل")
            return

        item = self.technicians_tree.item(selection[0])
        tech_id = item['values'][0]

        name = self.tech_name_var.get().strip()
        specialization = self.tech_spec_var.get().strip()
        phone = self.tech_phone_var.get().strip()

        if not name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم الفني")
            return

        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('UPDATE technicians SET name=?, specialization=?, phone=? WHERE id=?',
                          (name, specialization, phone, tech_id))
            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم تعديل الفني بنجاح")
            self.load_technicians()
            self.clear_technician_fields()
            self.main_window.refresh_all_tabs()

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تعديل الفني: {str(e)}")

    def delete_technician(self):
        """حذف الفني المحدد"""
        selection = self.technicians_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار فني للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف الفني؟"):
            item = self.technicians_tree.item(selection[0])
            tech_id = item['values'][0]

            try:
                conn = self.db.get_connection()
                cursor = conn.cursor()
                cursor.execute('DELETE FROM technicians WHERE id=?', (tech_id,))
                conn.commit()
                conn.close()

                messagebox.showinfo("نجح", "تم حذف الفني بنجاح")
                self.load_technicians()
                self.clear_technician_fields()
                self.main_window.refresh_all_tabs()

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف الفني: {str(e)}")

    def clear_technician_fields(self):
        """مسح حقول الفني"""
        self.tech_name_var.set('')
        self.tech_spec_var.set('')
        self.tech_phone_var.set('')

    def on_technician_select(self, event):
        """عند اختيار فني"""
        selection = self.technicians_tree.selection()
        if not selection:
            return

        item = self.technicians_tree.item(selection[0])
        values = item['values']

        self.tech_name_var.set(values[1])
        self.tech_spec_var.set(values[2] or '')
        self.tech_phone_var.set(values[3] or '')

    # وظائف إعدادات التقرير
    def browse_logo(self):
        """تصفح ملف الشعار"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف الشعار",
            filetypes=[("Image files", "*.png *.jpg *.jpeg *.gif *.bmp")]
        )

        if file_path:
            self.logo_path_var.set(file_path)

    def save_report_settings(self):
        """حفظ إعدادات التقرير"""
        try:
            arabic_header = self.arabic_text_widget.get("1.0", tk.END).strip()
            english_header = self.english_text_widget.get("1.0", tk.END).strip()
            footer_address = self.footer_address_var.get().strip()
            footer_email = self.footer_email_var.get().strip()
            logo_path = self.logo_path_var.get().strip()

            conn = self.db.get_connection()
            cursor = conn.cursor()

            # التحقق من وجود إعدادات سابقة
            cursor.execute('SELECT id FROM report_settings WHERE id = 1')
            existing = cursor.fetchone()

            if existing:
                # تحديث الإعدادات الموجودة
                cursor.execute('''
                    UPDATE report_settings
                    SET header_arabic=?, header_english=?, footer_address=?, footer_email=?, logo_path=?
                    WHERE id=1
                ''', (arabic_header, english_header, footer_address, footer_email, logo_path))
            else:
                # إدراج إعدادات جديدة
                cursor.execute('''
                    INSERT INTO report_settings (id, header_arabic, header_english, footer_address, footer_email, logo_path)
                    VALUES (1, ?, ?, ?, ?, ?)
                ''', (arabic_header, english_header, footer_address, footer_email, logo_path))

            conn.commit()
            conn.close()

            messagebox.showinfo("نجح", "تم حفظ إعدادات التقرير بنجاح")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ الإعدادات: {str(e)}")

    def load_report_settings(self):
        """تحميل إعدادات التقرير"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT header_arabic, header_english, footer_address, footer_email, logo_path FROM report_settings WHERE id = 1')
            result = cursor.fetchone()
            conn.close()

            if result:
                arabic_header, english_header, footer_address, footer_email, logo_path = result

                # تحميل النصوص
                self.arabic_text_widget.delete("1.0", tk.END)
                self.arabic_text_widget.insert("1.0", arabic_header or "")

                self.english_text_widget.delete("1.0", tk.END)
                self.english_text_widget.insert("1.0", english_header or "")

                # تحميل الفوتر
                self.footer_address_var.set(footer_address or "")
                self.footer_email_var.set(footer_email or "")
                self.logo_path_var.set(logo_path or "")
            else:
                # إعدادات افتراضية
                default_arabic = "جمهورية العراق\nوزارة الصحة\nقسم الصحة العامة\nمختبر الصحة العامة\nوحدة الفايروسات"
                default_english = "Republic of Iraq\nMinistry of Health\nPublic Health Department\nPublic Health Laboratory\nVirus Unit"

                self.arabic_text_widget.delete("1.0", tk.END)
                self.arabic_text_widget.insert("1.0", default_arabic)

                self.english_text_widget.delete("1.0", tk.END)
                self.english_text_widget.insert("1.0", default_english)

                self.footer_address_var.set("ذي قار /الناصرية /سومر/ مجاور حسينية السفراء الاربعة")
                self.footer_email_var.set("<EMAIL>")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل الإعدادات: {str(e)}")

    def show(self):
        """عرض التبويب"""
        self.frame.pack(fill=tk.BOTH, expand=True)

    def hide(self):
        """إخفاء التبويب"""
        self.frame.pack_forget()

    def refresh(self):
        """تحديث التبويب"""
        self.load_sample_types()
        self.load_senders()
        self.load_tests()
        self.load_technicians()
        self.load_report_settings()
